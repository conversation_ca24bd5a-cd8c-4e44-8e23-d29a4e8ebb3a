import { UserCV } from '@/store/user-atom';
import { AdminSimulation } from '../simulation';
import { User } from '../user/types';

export enum JobCandidateQueryKeys {
  GET_CANDIDATE_BY_JOB = 'getCandidateByJob',
  GET_CANDIDATE_BY_ORG = 'getCandidateByOrg',
}

export const LIMIT = 10;

export interface OrgCandidateListParams {
  page: number;
  limit: number;
  jobId?: string;
  email?: string;
  name?: string;
  status?: 'active' | 'completed';
  orgId?: string;
}

export interface ListJobCandidateParams {
  limit?: number;
  page?: number;
  title?: string;
  location?: string;
  categories?: string;
  jobType?: string;
  salary?: string;
  level?: string;
}

export interface JobCandidate {
  _id: string;
  userId: string;
  email: string;
  simulationId: string;
  jobId?: string;
  orgId?: string;
  status: 'active' | 'completed';
  // TODO: startedAt is deprecated
  startedAt: Date;
  appliedAt: Date;
  completedAt?: Date;
  scores?: number;
  matchPercentage?: number;
  aiEvaluation?: {
    summary?: string;
    areasForImprovement?: string[];
    strengths?: string[];
    personalities?: { name: string; explanation: string }[];
    tasks?: {
      title: string;
      description: string;
      submissions?: any[];
      exampleSubmission?: string;
    }[];
    skills?: {
      hardSkills?: { name: string; rating: number }[];
      softSkills?: { name: string; rating: number }[];
    };
    cvProcessResult?: {
      cvData: UserCV;
      result: {
        matchPercentage: number;
        overview?: string[];
        feedback?: string[];
        strengths?: string[];
        areasForImprovement?: string[];
        tasks?: {
          title: string;
          description: string;
          exampleSubmission: string;
        }[];
      };
    };
  };
  applyMode: 'cv' | 'simulation';
  user?: User;
  simulation?: AdminSimulation;
}
