import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useMutation } from '@tanstack/react-query';

export interface ProcessCVToApplyResponse {
  id: string;
  status: 'passed' | 'failed';
  position?: string;
  overview: string[];
  feedback: string[];
  score: number;
  matchPercentage: number;
  tasks: { title: string; description: string; exampleSubmission: string }[];
}

async function processCVToApply(params: {
  jobId: string;
  simulationId: string;
}): Promise<ProcessCVToApplyResponse | null> {
  // await new Promise((resolve) => setTimeout(resolve, 10000)); // simulate 6s delay
  // return null;
  const response = await axiosInstance.post<ProcessCVToApplyResponse>(
    API_ENDPONTS.PROCESS_CV_TO_APPLY,
    params
  );
  return response.data;
}

async function applyCV(params: { processId: string }): Promise<any> {
  const response = await axiosInstance.post<any>(API_ENDPONTS.APPLY_CV, params);
  return response.data;
}

export const useProcessCVToApply = () => {
  return useMutation({
    mutationFn: (params: { jobId: string; simulationId: string }) =>
      processCVToApply(params),
    // onError: (error: any) => {
    //   // let errMsg = 'Failed to process CV. Please try again later.';
    //   // if (error instanceof AxiosError && error.status === 400) {
    //   //   errMsg = error.response?.data?.message || errMsg;
    //   // }
    //   // toast.error(errMsg);
    // },
  });
};

export const useApplyCV = () => {
  return useMutation({
    mutationFn: (params: { processId: string }) => applyCV(params),
  });
};
