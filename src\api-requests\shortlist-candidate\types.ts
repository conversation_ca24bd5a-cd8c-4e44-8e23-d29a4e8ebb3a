import { Job } from '../job';
import { JobCandidate } from '../job-candidate/types';

export const ShortlistCandidateQueryKeys = {
  CREATE_SHORTLIST_CANDIDATE: 'createShortlistCandidate',
  BULK_CREATE_SHORTLIST_CANDIDATE: 'bulkCreateShortlistCandidate',
  BULK_DELETE_SHORTLIST_CANDIDATE: 'bulkDeleteShortlistCandidate',
  DELETE_SHORTLIST_CANDIDATE: 'deleteShortlistCandidate',
  DELETE_SHORTLIST_CANDIDATE_BY_ID_AND_ORG:
    'deleteShortlistCandidateByIdAndOrg',
  LIST_SHORTLIST_CANDIDATE_BY_ORG: 'listShortlistCandidateByOrg',
};

export interface CreateParams {
  candidateId: string;
  orgId: string;
  shortlistId: string;
}
export interface BulkCreateParams {
  candidateIds: string[];
  orgId: string;
  shortlistId: string;
}

export interface ShortlistCandidate extends JobCandidate {
  // _id: string;
  // userId: string;
  // email: string;
  // simulationId: string;
  // jobId?: string;
  // orgId?: string;
  // status: 'active' | 'completed';
  // startedAt: Date;
  // completedAt?: Date;
  // scores?: number;
  // matchPercentage?: number;
  // aiEvaluation?: {
  //   summary: string;
  // };
  // user?: User;
  candidateId: string;
  shortlistId: string;
  shortlist?: {
    _id: string;
    name: string;
  };
  job: Job;
}
