import { UserCV } from '@/store/user-atom';

export const UserProfileQueryKeys = {
  GET_USER_PROFILE: 'getUserProfile',
  UPSERT_USER_PROFILE: 'upsertUserProfile',
};

export interface UpdateUserProfileParams {
  firstName: string;
  lastName: string;
  userId: string;
  file?: File | null;
  address?: string;
  phone?: string;
  city?: string;
  region?: string;
  country?: string;
  gender?: string;
}

export interface UserProfile {
  _id: string;
  id: string;
  firstName: string;
  lastName: string;
  fullName: string;
  userId: string;
  email: string;
  avatar?: string;
  address?: string;
  phone?: string;
  city?: string;
  region?: string;
  country?: string;
  gender?: string;
  cv?: UserCV;
}

export interface UserApplication {
  _id: string;
  jobId?: string;
  simulationId?: string;
  orgId?: string;
  userId: string;
  matchPercentage?: number;
  // TODO: compatibility, default: 'pending'
  applicationStatus?: string;
  job?: any;
  simulation?: any;
  org?: any;
  appliedAt?: string;
  withdrawnAt?: string;
}
