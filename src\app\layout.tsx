import type { Metada<PERSON> } from 'next';
import { <PERSON>eist, <PERSON>eist_Mono } from 'next/font/google';
import './globals.css';
import ReactQueryProvider from '@/api-requests/react-query-provider';
import { JotaiProvider } from '@/store/jobtai-provider';
import 'react-quill-new/dist/quill.snow.css';
import AuthProvider from '../providers/auth';
import { Toaster } from 'react-hot-toast';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Talent',
  description:
    'No more ghosted applications. Land better jobs through job simulations.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ReactQueryProvider>
          <JotaiProvider>
            <AuthProvider>
              {children}
              <Toaster containerClassName="toast__container !z-[99999]" />
            </AuthProvider>
          </JotaiProvider>
        </ReactQueryProvider>
      </body>
    </html>
  );
}
