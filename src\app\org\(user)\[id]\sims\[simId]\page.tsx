'use client';

import { useGetOrgPublicSimulation } from '@/api-requests/simulation/get-org-home-simulation';
import SimParticipants from '@/components/SimParticipants';
import StartSimulationButton from '@/components/StartSimulationButton';
import { orgAtom } from '@/store/organization-atom';
import cn from '@/utils/class-names';
import { useAtom } from 'jotai';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from 'rizzui/button';

const simulationLevel: Record<number, { name: string; classColor: string }> = {
  1: {
    name: 'Beginner',
    classColor: 'bg-[#CCFFE7] text-[#009A21] border border-[#009A21]',
  },
  2: {
    name: 'Intermediate',
    classColor: 'bg-[#FFE5CC] text-[#FF8C00] border border-[#FF8C00]',
  },
  3: {
    name: 'Advanced',
    classColor: 'bg-[#E5CCFF] text-[#800080] border border-[#800080]',
  },
};

const Loading = () => {
  return (
    <div className="flex h-screen flex-col items-center justify-center">
      <div className="h-6 w-6 animate-spin rounded-full border-2 border-gray-300 border-t-blue-500"></div>
    </div>
  );
};

const NoData = () => {
  return (
    <div className="flex h-screen flex-col items-center justify-center">
      <img src="/error/404.png" alt="Not found" />
    </div>
  );
};

export default function OrgPublicSimulationPage() {
  const { simId } = useParams<{ simId: string }>();
  const router = useRouter();

  const [org] = useAtom(orgAtom);

  const { data: simulation, isFetched } = useGetOrgPublicSimulation({
    orgId: org?._id || '',
    simId,
  });

  const handleBackToList = () => {
    // Navigate to /partner/<orgId>
    router.push(`/org/${org?._id}`);
  };

  if (!isFetched) return <Loading />;

  if (!simulation) return <NoData />;

  return (
    <div className="">
      {/* Back button */}
      <Button
        className="mb-6 flex items-center px-0 font-semibold"
        variant="flat"
        onClick={handleBackToList}
      >
        <ArrowLeft className="mr-2" size={18} />
        Back
      </Button>

      {/* Card */}
      <div className="flex flex-col items-center gap-6 rounded-lg border-2 border-[#0D1321] bg-white p-6 shadow-md md:flex-row">
        {/* Image */}
        <div className="w-full flex-shrink-0 md:w-1/3">
          <img
            src={simulation.banner || '/simulation/default-banner.png'}
            onError={(e) => {
              e.currentTarget.src = '/simulation/default-banner.png';
            }}
            alt="Simulation"
            className="h-auto w-full rounded-xl"
          />
        </div>

        {/* Content */}
        <div className="flex-1">
          <h2 className="mb-2 text-xl font-semibold">{simulation.name}</h2>
          <p className="mb-4 text-gray-600">{simulation.description}</p>

          {/* Details */}
          <div className="space-y-2 text-sm">
            <p>
              <span className="font-medium text-[#0D1321]/50">Industry: </span>
              <span className="font-medium text-[#0D1321]">
                {simulation.industry}
              </span>
            </p>
            <p>
              <span className="font-medium text-[#0D1321]/50">Level: </span>
              <span
                className={cn(
                  'whitespace-nowrap rounded-full px-2 py-0.5 font-medium',
                  simulationLevel[simulation.level]?.classColor
                )}
              >
                {simulationLevel[simulation.level]?.name}:{' '}
                <span className="font-semibold">{simulation.minute} mins</span>
              </span>
            </p>
          </div>
        </div>
      </div>

      <div className="mt-5 flex flex-row justify-between">
        <SimParticipants
          candidates={simulation.candidates || []}
          total={simulation.joinedCount || 0}
          avatarClassName="h-10 w-10"
        />

        {simulation.progress?.status === 'completed' ? (
          <Button className="font-bold" type="button" variant="text" size="lg">
            Completed
          </Button>
        ) : (
          <StartSimulationButton
            simId={simulation.simulationId}
            buttonProps={{
              className: 'rounded-lg font-bold text-white',
              type: 'button',
              variant: 'solid',
              size: 'lg',
            }}
          >
            {simulation.progress?.status === 'active'
              ? 'Resume Simulation'
              : 'Start Simulation'}
            <ArrowRight className="ml-1" size={20} />
          </StartSimulationButton>
        )}
      </div>

      <div className="mt-5 space-y-3 rounded-lg bg-white p-5">
        <div>
          <p className="font-semibold text-[#0D1321]">Description</p>
          <div className="mt-2">{simulation.description}</div>
        </div>
        <div>
          <p className="font-semibold text-[#0D1321]">
            Benefits of Participating
          </p>
          <div className="mt-2">
            <ul className="list-disc pl-5">
              {simulation.benefits?.map((benefit, index) => (
                <li key={index}>{benefit}</li>
              ))}
            </ul>
          </div>
        </div>
        <div>
          <p className="font-semibold text-[#0D1321]">
            Skills You'll Practice & Be Assessed On
          </p>
          <div className="mt-2 flex flex-row flex-wrap justify-between gap-3 lg:justify-normal lg:gap-32">
            <div className="w-80 max-w-80 lg:w-auto">
              <p className="text-[#0D1321]">Hard Skills</p>
              <div className="mt-2">
                <ul className="list-disc pl-5">
                  {simulation.hardSkills?.map((skill, index) => (
                    <li key={index}>{skill}</li>
                  ))}
                </ul>
              </div>
            </div>
            <div className="w-80 max-w-80 lg:w-auto">
              <p className="text-[#0D1321]">Soft Skills</p>
              <div className="mt-2">
                <ul className="list-disc pl-5">
                  {simulation.softSkills?.map((skill, index) => (
                    <li key={index}>{skill}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
