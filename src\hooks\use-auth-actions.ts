/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { useSetAtom } from 'jotai';
import axiosInstance from '@/utils/http-client';
import { userAtom } from '@/store/user-atom';
import { API_DOMAINS } from '@/config/endpoint';

const AUTH_ENDPOINTS = {
  SIGNUP_REQUEST_CODE: 'api/auth/signup/request-code',
  SIGNUP_VERIFY: 'api/auth/signup/verify',
  LOGIN_PASSWORD: 'api/auth/login/password',
  LOGIN_REQUEST_CODE: 'api/auth/login/request-code',
  LOGIN_VERIFY: 'api/auth/login/verify',
  REFRESH: 'api/auth/refresh',
  LOGOUT: 'api/auth/logout',
};

export function useAuthActions() {
  const setUser = useSetAtom(userAtom);

  async function signupRequestCode(payload: {
    firstName?: string;
    lastName?: string;
    email: string;
    password: string;
    role: string;
    file?: File | null;
    organizationName?: string;
    description?: string;
    address?: string;
    city?: string;
    region?: string;
    country?: string;
    type?: string;
    orgId?: string;
  }) {
    try {
      const { data } = await axiosInstance.post(
        AUTH_ENDPOINTS.SIGNUP_REQUEST_CODE,
        payload,
        {
          baseURL: API_DOMAINS.BASE_URL,
          ...(payload.file
            ? {
                headers: {
                  'Content-Type': 'multipart/form-data',
                },
              }
            : {}),
        }
      );

      return data;
    } catch (error: any) {
      return {
        status: error?.response?.status || 400,
        message: error?.response?.data?.message || 'Failed to request code',
      };
    }
  }

  async function signupVerify(payload: { email: string; code: string }) {
    try {
      const { data } = await axiosInstance.post(
        AUTH_ENDPOINTS.SIGNUP_VERIFY,
        payload,
        {
          baseURL: API_DOMAINS.BASE_URL,
        }
      );
      if (data?.user) setUser(data.user);
      return data;
    } catch (error: any) {
      return {
        mensage: error?.response?.data?.message || 'Failed to verify code',
        status: error?.response?.status || 400,
      };
    }
  }

  async function loginPassword(payload: { email: string; password: string }) {
    try {
      const { data } = await axiosInstance.post(
        AUTH_ENDPOINTS.LOGIN_PASSWORD,
        payload,
        {
          baseURL: API_DOMAINS.BASE_URL,
        }
      );
      if (data?.user) setUser(data.user);
      return data;
    } catch (error: any) {
      return {
        status: error?.response?.status || 400,
        message: error?.response?.data?.message || 'Failed to login',
      };
    }
  }

  async function loginRequestCode(email: string) {
    try {
      const { data } = await axiosInstance.post(
        AUTH_ENDPOINTS.LOGIN_REQUEST_CODE,
        { email },
        {
          baseURL: API_DOMAINS.BASE_URL,
        }
      );
      return data;
    } catch (error: any) {
      return {
        status: error?.response?.status || 400,
        message: error?.response?.data?.message || 'Failed to login',
      };
    }
  }

  async function loginVerify(payload: { email: string; code: string }) {
    try {
      const { data } = await axiosInstance.post(
        AUTH_ENDPOINTS.LOGIN_VERIFY,
        payload,
        {
          baseURL: API_DOMAINS.BASE_URL,
        }
      );
      if (data?.user) setUser(data.user);
      return data;
    } catch (error: any) {
      return {
        status: error?.response?.status || 400,
        message: error?.response?.data?.message || 'Failed to login',
      };
    }
  }

  async function refresh() {
    try {
      const { data } = await axiosInstance.post(
        AUTH_ENDPOINTS.REFRESH,
        {},
        {
          baseURL: API_DOMAINS.BASE_URL,
        }
      );
      if (data?.user) setUser(data.user);
      return data;
    } catch (error: any) {
      return {
        status: error?.response?.status || 400,
        message: error?.response?.data?.message || 'Failed to login',
      };
    }
  }

  async function logout() {
    await axiosInstance.post(
      AUTH_ENDPOINTS.LOGOUT,
      {},
      {
        baseURL: API_DOMAINS.BASE_URL,
      }
    );
    setUser(null);
    window.location.href = '/';
  }

  return {
    signupRequestCode,
    signupVerify,
    loginPassword,
    loginRequestCode,
    loginVerify,
    refresh,
    logout,
  };
}
