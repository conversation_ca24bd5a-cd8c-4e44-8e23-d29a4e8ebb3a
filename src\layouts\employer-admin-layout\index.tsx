'use client';

import { useState } from 'react';
import { Drawer } from 'rizzui';
import Header from './header';
import Sidebar from './sidebar';

interface EmployerLayoutProps {
  children: React.ReactNode;
  isNew?: boolean;
}

export default function EmployerAdminLayout(props: EmployerLayoutProps) {
  const { children, isNew = false } = props;
  const [openSidebar, setOpenSidebar] = useState(false);

  return (
    <>
      {isNew ? (
        <main className="relative min-h-screen bg-[#F8F8F8]">
          <div className="lg:flex lg:flex-row">
            {/* TODO: use hook to check if mobile or desktop */}
            {/* Desktop */}
            <div className="sticky top-0 hidden h-screen w-80 bg-white p-4 shadow-lg lg:block">
              <Sidebar />
            </div>

            {/* Mobile */}
            <div className="lg:hidden">
              <Header onOpenMenu={() => setOpenSidebar(true)} isNew />
              <Drawer
                isOpen={openSidebar}
                onClose={() => setOpenSidebar(false)}
                containerClassName="bg-white p-4 w-80"
                placement="left"
              >
                <Sidebar />
              </Drawer>
            </div>

            <div className="w-full p-2 lg:p-10">{children}</div>
          </div>
        </main>
      ) : (
        <main className="relative min-h-screen">
          <Header isNew />

          {children}
        </main>
      )}
    </>
  );
}
