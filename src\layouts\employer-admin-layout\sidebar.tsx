'use client';

import { OrganizationType } from '@/api-requests/organization';
import { useAuthActions } from '@/hooks/use-auth-actions';
import { orgAtom } from '@/store/organization-atom';
import { userAtom } from '@/store/user-atom';
import cn from '@/utils/class-names';
import { useAtom } from 'jotai';
import {
  ChevronDownIcon,
  FileCog,
  LayoutDashboard,
  PlayIcon,
  Settings,
} from 'lucide-react';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { Accordion, Avatar, Dropdown } from 'rizzui';
import logoImage from '../../../public/ic-io-logo-light.png';

const getOrgTypeName = (type: OrganizationType) => {
  switch (type) {
    case OrganizationType.COMPANY:
      return 'Employer';
    case OrganizationType.COMMUNITY:
      return 'Community';
    case OrganizationType.EDUCATION:
      return 'Education';
    default:
      return '';
  }
};

const getOrgSidebarMenu = (type: OrganizationType) => {
  if (
    type === OrganizationType.EDUCATION ||
    type === OrganizationType.COMMUNITY
  ) {
    return [
      // {
      //   icon: LayoutDashboard,
      //   label: 'Dashboard',
      //   key: 'Dashboard',
      //   activePaths: ['/org/admin'],
      //   link: '/org/admin',
      // },
      {
        icon: FileCog,
        label: 'Manage',
        key: 'Manage',
        activePaths: [
          '/org/admin/simulations',
          '/org/admin/candidates',
          '/org/admin/members',
          '/org/admin/subscribe',
        ],
        children: [
          {
            label: 'Simulation Library',
            key: 'Simulation Library',
            link: '/org/admin/simulations',
          },
          {
            label: 'Candidate List',
            key: 'Candidate List',
            link: '/org/admin/candidates',
          },
          { label: 'Members', key: 'Members', link: '/org/admin/members' },
          // {
          //   label: 'Subscribe',
          //   key: 'Subscribe',
          //   link: '/org/admin/subscribe',
          // },
        ],
      },
      {
        icon: Settings,
        label: 'Settings',
        key: 'Settings',
        activePaths: ['/org/admin/account-setting', '/org/admin/notifications'],
        children: [
          {
            label: 'Account',
            key: 'Account',
            link: '/org/admin/account-setting',
          },
          // {
          //   label: 'Notifications',
          //   key: 'Notifications',
          //   link: '/org/admin/notifications',
          // },
        ],
      },
    ];
  }
  if (type === OrganizationType.COMPANY) {
    return [
      // {
      //   icon: LayoutDashboard,
      //   label: 'Dashboard',
      //   key: 'Dashboard',
      //   activePaths: ['/org/admin'],
      //   link: '/org/admin',
      // },
      {
        icon: FileCog,
        label: 'Manage',
        key: 'Manage',
        activePaths: [
          '/org/admin/simulations',
          '/org/admin/candidates',
          '/org/admin/jobs',
          '/org/admin/members',
          '/org/admin/subscribe',
        ],
        children: [
          {
            label: 'Jobs',
            key: 'Jobs',
            link: '/org/admin/jobs',
          },
          {
            label: 'Candidates',
            key: 'Candidates',
            link: '/org/admin/candidates',
          },
          {
            label: 'Talent Search',
            key: 'Talent Search',
            link: '/org/admin/talents',
          },
          // { label: 'Members', key: 'Members', link: '/org/admin/members' },
          // {
          //   label: 'Subscribe',
          //   key: 'Subscribe',
          //   link: '/org/admin/subscribe',
          // },
        ],
      },
      {
        icon: Settings,
        label: 'Settings',
        key: 'Settings',
        activePaths: ['/org/admin/account-setting', '/org/admin/notifications'],
        children: [
          {
            label: 'Account Setting',
            key: 'Account Profile',
            link: '/org/admin/account-setting',
          },
          {
            label: 'Company Profile',
            key: 'Company Profile',
            link: '/org/admin/company-profile',
          },
          // {
          //   label: 'Notifications',
          //   key: 'Notifications',
          //   link: '/org/admin/notifications',
          // },
        ],
      },
    ];
  }
  return [];
};

export default function Sidebar() {
  const [user] = useAtom(userAtom);
  const [org] = useAtom(orgAtom);
  const router = useRouter();
  const pathname = usePathname();

  const { logout } = useAuthActions();

  const menu = getOrgSidebarMenu(org?.type! as OrganizationType);

  return (
    <div className="flex h-full flex-col">
      <div className="flex h-16 flex-row items-center justify-center border-b text-lg">
        <Image
          src={logoImage}
          alt="Industry Connect Logo"
          width={0}
          height={32}
          className="h-6 w-auto lg:h-8"
          loader={({ src }) => src}
        />
        <span className="ml-2 rounded bg-[#0D1321] px-2 py-1 text-xs text-white">
          {getOrgTypeName(org?.type! as OrganizationType)}
        </span>
      </div>

      <nav className="flex-1 space-y-2 overflow-y-auto px-2 py-4">
        {menu.map((item) =>
          item.children ? (
            <Accordion
              key={item.key}
              className=""
              defaultOpen={
                item.activePaths.find((path) => pathname.includes(path)) !==
                  undefined ||
                (item.children || []).find((child) =>
                  pathname.includes(child.link)
                ) !== undefined
              }
            >
              <Accordion.Header>
                {({ open }) => (
                  <div
                    className={cn(
                      'flex w-full cursor-pointer flex-row items-center rounded-md px-3 py-2 font-medium',
                      item.children.find((child) =>
                        pathname.includes(child.link)
                      ) !== undefined && 'bg-[#0D1321] text-white'
                    )}
                  >
                    {item.icon && <item.icon className="mr-4 h-5 w-5" />}
                    {item.label}
                    <ChevronDownIcon
                      className={cn(
                        'ml-auto h-4 w-4 -rotate-90 transform transition-transform duration-300',
                        open && 'rotate-0'
                      )}
                    />
                  </div>
                )}
              </Accordion.Header>
              <Accordion.Body>
                <div className="ml-4 mt-1 flex flex-col space-y-1">
                  {item.children.map((child) => (
                    <button
                      key={child.key}
                      onClick={() => {
                        if (child.link) {
                          router.push(child.link);
                        }
                      }}
                      className={cn(
                        'flex cursor-pointer flex-row items-center rounded-md px-4 py-3 text-left text-sm',
                        pathname.includes(child.link)
                          ? 'border-b-2 border-primary'
                          : 'hover:bg-gray-100'
                      )}
                    >
                      <PlayIcon
                        className="mr-2 h-2 w-2 text-[#000]"
                        fill="#000"
                      />
                      {child.label}
                    </button>
                  ))}
                </div>
              </Accordion.Body>
            </Accordion>
          ) : (
            <button
              key={item.key}
              onClick={() => {
                if ((item as any).link) {
                  router.push((item as any).link);
                }
              }}
              className={cn(
                'flex w-full flex-row items-center rounded-md px-3 py-2 text-left font-medium',
                item.activePaths.find((path) => pathname.includes(path)) !==
                  undefined
                  ? 'bg-primary text-white'
                  : 'hover:bg-gray-100'
              )}
            >
              {item.icon && <item.icon className="mr-4 h-5 w-5" />}
              {item.label}
            </button>
          )
        )}
      </nav>

      {/* Avatar */}
      <div className="mt-auto">
        <Dropdown>
          <Dropdown.Trigger>
            <div className="flex flex-row items-center gap-1">
              <Avatar
                name={`${user?.firstName} ${user?.lastName}`}
                src={user?.avatar}
                size="md"
                className="h-10 w-10 cursor-pointer rounded-full object-cover"
              />
              <div>
                {user?.firstName} {user?.lastName}
              </div>
            </div>
          </Dropdown.Trigger>
          <Dropdown.Menu className="w-fit divide-y">
            <Dropdown.Item>
              {user?.firstName} {user?.lastName}
            </Dropdown.Item>
            <Dropdown.Item className="hover:bg-primary hover:text-white">
              {/* <Link href="/profile">Profile</Link> */}
              Profile
            </Dropdown.Item>
            <Dropdown.Item
              className="hover:bg-primary hover:text-white"
              onClick={logout}
            >
              Logout
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>
      </div>
    </div>
  );
}
