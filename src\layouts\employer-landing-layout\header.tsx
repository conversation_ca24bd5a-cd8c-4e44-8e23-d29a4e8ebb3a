import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import cn from '@/utils/class-names';
import logoImage from '../../../public/ic-io-logo-light.png';
import { useAtom } from 'jotai';
import { resetFiltersAtom, resetSearchAtom } from '@/store/job-atom';
import { Button } from 'rizzui/button';
import { useState } from 'react';
import SignInModal from '@/views/auth/sign-in-up/sign-in-modal';
import { Role } from '@/api-requests/user/types';
import { userAtom } from '@/store/user-atom';
import { useAuthActions } from '@/hooks/use-auth-actions';
import { Dropdown } from 'rizzui/dropdown';
import { Avatar } from 'rizzui/avatar';
import { useRouter } from 'next/navigation';

export default function Header() {
  const pathname = usePathname();

  const [, resetFilters] = useAtom(resetFiltersAtom);
  const [, resetSearch] = useAtom(resetSearchAtom);
  const [user] = useAtom(userAtom);

  const router = useRouter();

  const { logout } = useAuthActions();

  const [openLoginModal, setOpenLoginModal] = useState(false);

  const navItems = [
    { label: 'For Employer', href: '/employer/landing' },
    { label: 'Pricing', href: '/employer/pricing' },
    // { label: "Resources", href: "/resources" },
    // { label: "Support", href: "/support" },
  ];

  const handleLogoClick = () => {
    resetFilters();
    resetSearch();
  };

  return (
    <>
      <header className="sticky top-0 z-[100] flex h-[80px] items-center bg-white px-4 shadow-md xl:px-0">
        <div className="mx-auto flex w-full max-w-[1200px] items-center justify-between">
          {/* Logo */}
          <Link onClick={handleLogoClick} href="/">
            <div className="flex items-center space-x-2">
              <Image
                src={logoImage}
                alt="Industry Connect Logo"
                width={0}
                height={32}
                className="h-8 w-auto"
                loader={({ src }) => src}
              />
            </div>
          </Link>

          {/* Menu */}
          <nav className="hidden h-full items-center space-x-8 text-sm font-medium text-gray-600 md:flex">
            {navItems.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link key={item.href} href={item.href}>
                  <span
                    className={cn(
                      'relative cursor-pointer transition-colors',
                      isActive ? 'font-bold text-primary' : 'hover:text-primary'
                    )}
                  >
                    {item.label}
                    {isActive && (
                      <span className="absolute -bottom-[32px] left-0 h-[3px] w-full bg-primary" />
                    )}
                  </span>
                </Link>
              );
            })}
          </nav>

          {/* Sign In Button */}
          <div className="flex h-full items-center space-x-4">
            {user && user.role === Role.EMPLOYER ? (
              <Dropdown>
                <Dropdown.Trigger>
                  <Avatar
                    name={`${user.firstName} ${user.lastName}`}
                    src={user.avatar}
                    size="md"
                    className="h-10 w-10 cursor-pointer rounded-full object-cover"
                  />
                </Dropdown.Trigger>
                <Dropdown.Menu className="w-fit divide-y">
                  <Dropdown.Item className="hover:bg-primary hover:text-white">
                    <Link href={`/employer/admin/profile`}>Profile</Link>
                  </Dropdown.Item>
                  <Dropdown.Item
                    className="hover:bg-primary hover:text-white"
                    onClick={logout}
                  >
                    Logout
                  </Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            ) : (
              <Button
                className="bg-primary text-white"
                onClick={() => setOpenLoginModal(true)}
              >
                Sign In
              </Button>
            )}
          </div>
        </div>
      </header>

      {openLoginModal && (
        <SignInModal
          open={openLoginModal}
          onClose={() => setOpenLoginModal(false)}
          role={Role.EMPLOYER}
          onLoginSuccess={() => {
            console.log('Login successful ::: ', user);
            // TODO: For navigation after the login, we should consider using prop (redirectToDefault: true). Then the system will check the user role
            router.push('/org/admin');
          }}
        />
      )}
    </>
  );
}
