import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import cn from '@/utils/class-names';
import logoImage from '../../../public/ic-io-logo-light.png';
import { useAtom } from 'jotai';
import { resetFiltersAtom, resetSearchAtom } from '@/store/job-atom';
import SignInModal from '@/views/auth/sign-in-up/sign-in-modal';
import { useState } from 'react';
import { Avatar, Button, Dropdown } from 'rizzui';
import { Role } from '@/api-requests/user/types';
import { userAtom } from '@/store/user-atom';
import { useAuthActions } from '@/hooks/use-auth-actions';

export default function Header() {
  const pathname = usePathname();
  const [, resetFilters] = useAtom(resetFiltersAtom);
  const [, resetSearch] = useAtom(resetSearchAtom);
  const [user] = useAtom(userAtom);

  const { logout } = useAuthActions();

  const [openLoginModal, setOpenLoginModal] = useState(false);

  const navItems = [
    { label: 'For Job Seekers', href: '/find-jobs' },
    // { label: "How It Work", href: "/how-it-work" },
    { label: 'For Employers', href: '/employer/landing' },
    // { label: "My Profile", href: "/profile" },
  ];

  const handleLogoClick = () => {
    resetFilters();
    resetSearch();
  };

  return (
    <>
      <header className="sticky top-0 z-[100] flex h-[80px] items-center bg-white px-4 shadow-md md:px-5 lg:px-6">
        <div className="mx-auto flex w-full max-w-[1440px] items-center justify-between">
          {/* Logo */}
          <Link onClick={handleLogoClick} href="/">
            <div className="flex items-center space-x-2">
              <Image
                src={logoImage}
                alt="Industry Connect Logo"
                width={0}
                height={32}
                className="h-8 w-auto"
                loader={({ src }) => src}
              />
            </div>
          </Link>

          {/* Menu */}
          <nav className="hidden h-full items-center space-x-8 text-sm font-medium text-gray-600 md:flex">
            {navItems.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link key={item.href} href={item.href}>
                  <span
                    className={cn(
                      'relative cursor-pointer transition-colors',
                      isActive ? 'font-bold text-primary' : 'hover:text-primary'
                    )}
                  >
                    {item.label}
                    {isActive && (
                      <span className="absolute -bottom-[32px] left-0 h-[3px] w-full bg-primary" />
                    )}
                  </span>
                </Link>
              );
            })}
          </nav>

          {/* Sign In Button */}
          <div className="flex h-full items-center space-x-4">
            {user ? (
              <Dropdown>
                <Dropdown.Trigger>
                  <Avatar
                    name={`${user.firstName} ${user.lastName}`}
                    src={user.avatar || '/avatar/user-default.png'}
                    size="md"
                    className="h-10 w-10 cursor-pointer rounded-full object-cover !bg-transparent"
                  />
                </Dropdown.Trigger>
                <Dropdown.Menu className="w-fit divide-y">
                  <Dropdown.Item className="hover:bg-primary hover:text-white">
                    <Link href={`/profile/${user.id}`}>Profile</Link>
                  </Dropdown.Item>
                  <Dropdown.Item
                    className="hover:bg-primary hover:text-white"
                    onClick={logout}
                  >
                    Logout
                  </Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            ) : (
              <Button
                className="bg-primary text-white"
                onClick={() => setOpenLoginModal(true)}
              >
                Sign In
              </Button>
            )}
          </div>
        </div>
      </header>

      {openLoginModal && (
        <SignInModal
          open={openLoginModal}
          onClose={() => setOpenLoginModal(false)}
          role={Role.USER}
        />
      )}
    </>
  );
}
