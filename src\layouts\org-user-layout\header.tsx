'use client';

import { OrganizationType } from '@/api-requests/organization/types';
import { Role } from '@/api-requests/user/types';
import { useAuthActions } from '@/hooks/use-auth-actions';
import { orgAtom } from '@/store/organization-atom';
import { userAtom } from '@/store/user-atom';
import SignInModal from '@/views/auth/sign-in-up/sign-in-modal';
import MessageIcon from '@/views/icons/message';
import NotificationIcon from '@/views/icons/notification';
import { useAtom } from 'jotai';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import { Avatar, Button, Dropdown } from 'rizzui';

const getOrgBadge = (type?: OrganizationType) => {
  switch (type) {
    case OrganizationType.COMMUNITY:
      return 'Community';
    case OrganizationType.COMPANY:
      return 'Company';
    case OrganizationType.EDUCATION:
      return 'Education';
    default:
      return '';
  }
};

export default function Header() {
  const [user] = useAtom(userAtom);
  const [org] = useAtom(orgAtom);

  const { logout } = useAuthActions();

  const [openLoginModal, setOpenLoginModal] = useState(false);

  return (
    <>
      <header className="sticky top-0 z-[100] flex h-[80px] items-center bg-white px-4 shadow-md xl:px-0">
        <div className="mx-auto flex w-full items-center justify-between px-4 lg:px-6">
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-4">
              <Link href="/">
                <div className="flex items-center space-x-2">
                  <Image
                    src="/ic-io-logo-light.png"
                    alt="Industry Connect Logo"
                    width={0}
                    height={32}
                    className="h-8 w-auto"
                    loader={({ src }) => src}
                  />
                </div>
              </Link>
              <div className="hover:cursor-pointer">
                {getOrgBadge(org?.type)}
              </div>
            </div>
          </div>

          {user ? (
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <MessageIcon />
                  <span className="absolute right-0 top-0 h-2 w-2 -translate-y-[25%] rounded-full bg-primary" />
                </div>

                <div className="relative">
                  <NotificationIcon />
                  <span className="absolute right-0 top-0 h-2 w-2 -translate-y-[25%] rounded-full bg-primary" />
                </div>
              </div>

              <div className="h-6 w-px bg-gray-300" />

              <div className="flex h-full items-center space-x-4">
                <Dropdown>
                  <Dropdown.Trigger>
                    <Avatar
                      name={`${user.firstName} ${user.lastName}`}
                      src={user.avatar || '/avatar/user-default.png'}
                      size="md"
                      className="h-10 w-10 cursor-pointer rounded-full object-cover !bg-transparent"
                    />
                  </Dropdown.Trigger>
                  <Dropdown.Menu className="w-fit divide-y">
                    <Dropdown.Item className="hover:bg-primary hover:text-white">
                      <Link href={`/profile/${user.id}`}>Profile</Link>
                    </Dropdown.Item>
                    <Dropdown.Item
                      className="hover:bg-primary hover:text-white"
                      onClick={logout}
                    >
                      Logout
                    </Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown>
              </div>
            </div>
          ) : (
            <Button
              className="bg-primary text-white"
              onClick={() => setOpenLoginModal(true)}
            >
              Sign In
            </Button>
          )}
        </div>
      </header>

      {openLoginModal && (
        <SignInModal
          open={openLoginModal}
          onClose={() => setOpenLoginModal(false)}
          role={Role.EMPLOYER}
        />
      )}
    </>
  );
}
