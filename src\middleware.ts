import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // const pathname = request.nextUrl.pathname;

  // if (pathname === '/' || pathname === '') {
  //   const url = new URL('/find-jobs', request.url);

  //   return NextResponse.redirect(url);
  // }

  const countryName = request.headers.get('cloudfront-viewer-country-name');

  console.log('🌍 Country:', countryName);

  const response = NextResponse.next();

  if (countryName) {
    // response.headers.set('x-country-name', countryName);

    response.cookies.set('country-name', countryName, {
      path: '/',
      httpOnly: false,
      secure: true,
      sameSite: 'lax',
    });
  }

  return response;
}

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|images|favicon.ico|firebase-messaging-sw.js$).*)',
  ],
};
