'use client';

import dynamic from 'next/dynamic';

const JobSearch = dynamic(() => import('./job-search'), {
  ssr: false,
});
const JobFilter = dynamic(() => import('./job-filter'), {
  ssr: false,
});

export default function JobBanner() {
  return (
    <section
      className="relative bg-cover bg-center bg-no-repeat p-4 xl:px-0"
      style={{ backgroundImage: 'url("/job/job-hero-bg.png")' }}
    >
      <div className="mx-auto mb-8 mt-10 flex max-w-[1440px] flex-col gap-6">
        <div>
          <h1 className="mb-2 text-3xl font-bold text-primary md:text-4xl">
            Try the Job Before You Apply
          </h1>
          <p className="mb mx-auto text-base text-gray-600 md:text-lg">
            No more ghosted applications. Land better jobs through job
            simulations.
          </p>
        </div>

        <JobSearch />

        <JobFilter />
      </div>
    </section>
  );
}
