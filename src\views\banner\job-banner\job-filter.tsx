'use client';

import {
  current<PERSON><PERSON><PERSON>tom,
  difficultyAtom,
  categoriesAtom,
  jobType<PERSON>tom,
  resetFilters<PERSON>tom,
  salaryAtom,
} from '@/store/job-atom';
import { useAtom } from 'jotai';
import { useEffect, useRef } from 'react';
import { MultiSelect, MultiSelectOption, Checkbox, Input } from 'rizzui';
import debounce from 'lodash/debounce';
import cn from '@/utils/class-names';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useURLParamsManager } from '@/hooks/use-update-url-params';

interface Option {
  label: string;
  value: string;
  description?: string;
}

const jobTypeOptions: Option[] = [
  { label: 'Full Time', value: 'fulltime' },
  { label: 'Part Time', value: 'parttime' },
  { label: 'Contract', value: 'contract' },
  { label: 'Remote', value: 'remote' },
];

const difficultyOptions: Option[] = [
  { label: 'Beginner', value: '1' },
  { label: 'Intermediate', value: '2' },
  { label: 'Advanced', value: '3' },
];

const salaryOptions: Option[] = [
  { label: '$0 - $5,000', value: '0-5000' },
  { label: '$5,000 - $10,000', value: '5000-10000' },
  { label: '$10,000 - $15,000', value: '10000-15000' },
  { label: '$15,000 - $20,000', value: '15000-20000' },
  { label: '$20,000 - $25,000', value: '20000-25000' },
  { label: '$25,000 - $30,000', value: '25000-30000' },
  { label: '$30,000 - $35,000', value: '30000-35000' },
  { label: '$35,000 - $40,000', value: '35000-40000' },
  { label: '$40,000 - $45,000', value: '40000-45000' },
  { label: '$45,000 - $50,000', value: '45000-50000' },
  { label: '$50,000+', value: '50000+' },
];

// const industryOptions: Option[] = [
//   { label: 'Technology', value: 'technology' },
//   { label: 'Healthcare', value: 'healthcare' },
//   { label: 'Finance', value: 'finance' },
//   { label: 'Education', value: 'education' },
// ];

function renderOptionDisplayValue(
  option: MultiSelectOption,
  selected: boolean
) {
  return (
    <div
      className="flex w-full cursor-pointer items-center justify-between gap-2 py-1"
      onClick={(e) => {
        e.currentTarget.dispatchEvent(
          new MouseEvent('mousedown', { bubbles: true })
        );
      }}
    >
      <div className="flex items-center gap-1">
        <span className="text-sm">{option.label}</span>
        {option.description && (
          <span className="text-xs text-gray-500">({option.description})</span>
        )}
      </div>
      <Checkbox
        variant="flat"
        size="sm"
        checked={selected}
        onClick={(e) => {
          e.stopPropagation();
          e.currentTarget.dispatchEvent(
            new MouseEvent('mousedown', { bubbles: true })
          );
        }}
        readOnly
        // iconClassName="bg-white text-primary"
        // className='bg-white'
        // inputClassName='bg-white'
      />
    </div>
  );
}

function renderDisplayValue(
  selectedItems: string[],
  options: MultiSelectOption[],
  title: string
) {
  const filteredItems = options.filter((option) =>
    selectedItems.includes(option.value)
  );
  // const isLongerThanTwo = filteredItems.length > 1;

  return (
    <div className={cn('flex w-full flex-wrap items-center text-start')}>
      <div className="">{title}</div>
      {/* {filteredItems.length > 0 && ( */}
      <span className="border-muted ms-2 border-s ps-2">
        {filteredItems.length} Selected
      </span>
    </div>
  );
}

export default function JobFilter() {
  const [jobTypes, setJobTypes] = useAtom(jobTypeAtom);
  const [difficulty, setDifficulty] = useAtom(difficultyAtom);
  const [salary, setSalary] = useAtom(salaryAtom);
  const [categories, setCategories] = useAtom(categoriesAtom);
  const [, resetFilters] = useAtom(resetFiltersAtom);
  const [, setPage] = useAtom(currentPageAtom);

  const searchInputRef = useRef<HTMLInputElement>(null);

  const router = useRouter();
  const pathname = usePathname();
  const urlSearchParams = useSearchParams();

  const urlManager = useURLParamsManager();

  const handleFilterChange =
    (setter: (value: string[]) => void) =>
    (value: string[], paramKey: string) => {
      setter(value);
      setPage(1);

      urlManager.update({
        [paramKey]: value,
        page: 1,
      });
    };

  const handleClearAll = () => {
    resetFilters();
    urlManager.clear();
  };

  const debouncedSetCategories = useRef(
    debounce((value: string) => {
      setCategories(value);
      setPage(1);
      urlManager.update({
        categories: value,
        page: 1,
      });
    }, 500)
  ).current;

  // Cleanup khi component unmount
  useEffect(() => {
    return () => {
      debouncedSetCategories.cancel();
    };
  }, [debouncedSetCategories]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    debouncedSetCategories(value);
  };

  useEffect(() => {
    if (searchInputRef.current && searchInputRef.current.value !== categories) {
      searchInputRef.current.value = categories;
    }
  }, [categories]);

  useEffect(() => {
    if (pathname === '/') {
      if (
        jobTypes.length > 0 ||
        difficulty.length > 0 ||
        salary.length > 0 ||
        categories
      ) {
        router.push('/find-jobs');
      }
    }
  }, [pathname, jobTypes, difficulty, salary, categories, router]);

  useEffect(() => {
    const categories = urlSearchParams.get('categories') || '';
    const jobType = urlSearchParams.get('jobType') || '';
    const salary = urlSearchParams.get('salary') || '';
    const level = urlSearchParams.get('level') || '';
    const pageFromUrl = parseInt(urlSearchParams.get('page') || '1');

    setJobTypes(jobType ? jobType.split(',') : []);
    setDifficulty(level ? level.split(',') : []);
    setSalary(salary ? salary.split(',') : []);
    setCategories(categories);
    setPage(pageFromUrl || 1);
  }, [urlSearchParams, setJobTypes, setDifficulty, setSalary, setCategories, setPage]);

  return (
    <div className="flex w-full flex-wrap items-center gap-4">
      <span className="text-base text-gray-600">Filters</span>

      <div
        className="flex flex-wrap items-center gap-4"
        // className="grid grid-cols-1 gap-5 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-4"
      >
        <MultiSelect
          clearable
          value={jobTypes}
          options={jobTypeOptions}
          onChange={(val: string[]) =>
            handleFilterChange(setJobTypes)(val, 'jobType')
          }
          onClear={() => handleFilterChange(setJobTypes)([], 'jobType')}
          getOptionDisplayValue={renderOptionDisplayValue}
          displayValue={(selectedItems, options) =>
            renderDisplayValue(selectedItems, options, 'Job Type')
          }
          placeholder="Job Type"
          className="w-full min-w-[220px] border-none shadow-[0_1px_3px_0_rgba(0,0,0,0.08)] transition-all duration-300 ease-in-out hover:shadow-[0_4px_12px_0_rgba(0,0,0,0.15)] sm:w-auto [&>.rizzui-popover]:rounded-xl [&>.rizzui-popover]:shadow-md"
        />

        <MultiSelect
          clearable
          value={difficulty}
          options={difficultyOptions}
          // onChange={handleFilterChange(setDifficulty)}
          onChange={(val: string[]) =>
            handleFilterChange(setDifficulty)(val, 'level')
          }
          onClear={() => handleFilterChange(setDifficulty)([], 'level')}
          getOptionDisplayValue={renderOptionDisplayValue}
          displayValue={(selectedItems, options) =>
            renderDisplayValue(selectedItems, options, 'Difficulty')
          }
          placeholder="Simulation Difficulty"
          className="w-full min-w-[220px] shadow-[0_1px_3px_0_rgba(0,0,0,0.08)] transition-all duration-300 ease-in-out hover:shadow-[0_4px_12px_0_rgba(0,0,0,0.15)] sm:w-auto [&>.rizzui-popover]:rounded-xl [&>.rizzui-popover]:shadow-md"
        />

        <MultiSelect
          clearable
          value={salary}
          options={salaryOptions}
          onChange={(val: string[]) =>
            handleFilterChange(setSalary)(val, 'salary')
          }
          onClear={() => handleFilterChange(setSalary)([], 'salary')}
          getOptionDisplayValue={renderOptionDisplayValue}
          displayValue={(selectedItems, options) =>
            renderDisplayValue(selectedItems, options, 'Salary Range')
          }
          placeholder="Salary Range"
          className="w-full min-w-[220px] shadow-[0_1px_3px_0_rgba(0,0,0,0.08)] transition-all duration-300 ease-in-out hover:shadow-[0_4px_12px_0_rgba(0,0,0,0.15)] sm:w-auto [&>.rizzui-popover]:rounded-xl [&>.rizzui-popover]:shadow-md"
        />

        <Input
          ref={searchInputRef}
          placeholder="Search by Industry"
          className="w-full min-w-[220px] sm:w-auto"
          defaultValue={categories}
          onChange={handleSearchChange}
        />
      </div>

      <button
        className="text-sm text-gray-600 underline hover:text-black"
        onClick={handleClearAll}
      >
        Clean all
      </button>
    </div>
  );
}
