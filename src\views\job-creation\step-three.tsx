'use client';

import MoneyIcon from '../icons/money';
import ClockIcon from '../icons/clock';
import TechnologyIcon from '../icons/technology';
import { Avatar, Button } from 'rizzui';
import { safeFormatDistanceToNow } from '@/utils/date';
import { Organization } from '@/api-requests/organization';
import { FormValues } from './index';
import { useFormContext } from 'react-hook-form';
import { getJobTypeString } from '../job/job-list';
import { Job } from '@/api-requests/job';
import { orgAtom } from '@/store/organization-atom';
import { useAtom } from 'jotai';


export default function StepThree() {
  // const formattedDate = safeFormatDistanceToNow(new Date(), {
  //   addSuffix: true,
  // });

  const [org] = useAtom(orgAtom);

  const { watch } = useFormContext<FormValues>();

  const salary = watch('salary');
  const description = watch('description');
  const skills = watch('skills');
  const levels = watch('levels') as string[];
  const employment = watch('employment');
  const workplace = watch('workplace');

  const jobType = getJobTypeString({ ...workplace, ...employment } as Job);

  return (
    <div className="space-y-4">
      <div className="text-lg">Job summary</div>

      <div>
        <div>Basics infomation</div>
        <div className="mt-1 flex items-center gap-4 rounded-lg p-4 shadow-[0_4px_30px_rgba(0,0,0,0.15)]">
          <Avatar
            src={org?.logo || ''}
            name={org?.name || 'Organization Logo'}
            customSize={100}
          />

          <div>
            <p className="text-sm font-medium text-gray-700">
              {org?.name}
            </p>
            <p className="text-[12px] text-gray-500">
              {org?.location}
              {/* • {formattedDate} */}
            </p>
            <div className="mt-2 flex items-center gap-2 text-[10px] text-gray-500">
              <span className="flex items-center gap-1">
                <MoneyIcon className="h-3 w-3" />
                <span>
                  {(salary?.min as number).toLocaleString()} -{' '}
                  {(salary?.max as number).toLocaleString()}
                </span>
              </span>
              <span className="flex items-center gap-1">
                <ClockIcon className="h-3 w-3" />
                <span>{jobType || '-'}</span>
              </span>
              <span className="flex items-center gap-1">
                <TechnologyIcon className="h-3 w-3" />
                <span>
                  {levels?.length > 0
                    ? levels.map((level) => (
                        <span
                          key={level}
                          className="mb-2 mr-2 inline-flex items-center rounded-full bg-[#F4F4F4] px-3 py-1 text-xs"
                        >
                          {level}
                        </span>
                      ))
                    : '-'}
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>

      <div>
        <div>Job Description</div>
        <div className="mt-1 space-y-3 rounded-lg p-4 shadow-[0_4px_30px_rgba(0,0,0,0.15)]">
          <section>
            <h2 className="mb-2 text-sm font-semibold text-gray-800">
              About this role
            </h2>
            <p
              className="text-justify text-sm text-gray-600"
              dangerouslySetInnerHTML={{
                __html: description || '-',
              }}
            ></p>
          </section>

          {/* Qualification */}
          {/* <section>
            <h2 className="mb-2 text-sm font-semibold text-gray-800">
              Qualification
            </h2>
            <ul className="list-disc space-y-1 pl-5 text-sm text-gray-600">
              <li>
                0-2 years of experience in data analysis or a related role.
              </li>
              <li>
                Solid understanding of statistics, data wrangling, and data
                visualization.
              </li>
              <li>
                Proficiency in Excel and at least one analytics tool (e.g. SQL,
                Python, or R).
              </li>
              <li>
                Experience with data visualization tools like Tableau or Power
                BI is a plus.
              </li>
              <li>Good communication skills and an analytical mindset.</li>
            </ul>
          </section> */}

          {/* Responsibility */}
          {/* <section>
            <h2 className="mb-2 text-sm font-semibold text-gray-800">
              Responsibility
            </h2>
            <ul className="list-disc space-y-1 pl-5 text-sm text-gray-600">
              <li>
                Collect, clean, and analyze data from multiple sources to
                identify trends and insights.
              </li>
              <li>
                Build and maintain dashboards and regular reports for different
                teams.
              </li>
              <li>
                Assist in designing and conducting A/B testing or other data
                experiments.
              </li>
              <li>
                Collaborate with business and technical teams to define data
                requirements.
              </li>
              <li>
                Present findings and actionable insights in a clear and concise
                way.
              </li>
            </ul>
          </section> */}

          {/* Additional Requirement */}
          {/* <section>
            <h2 className="mb-2 text-sm font-semibold text-gray-800">
              Additional Requirement
            </h2>
            <ul className="list-disc space-y-1 pl-5 text-sm text-gray-600">
              <li>
                Candidates must complete the Data Analyst Job Simulation
                provided by DataScope Inc and achieve a minimum score of 80% to
                proceed to the next stage of the recruitment process.
              </li>
            </ul>
          </section> */}

          <section>
            <h2 className="mb-2 text-sm font-semibold text-gray-800">
              Required skills
            </h2>
            <div>
              {skills?.length > 0
                ? skills.map((skill) => (
                    <span
                      key={skill}
                      className="mb-2 mr-2 inline-flex items-center rounded-full bg-[#F4F4F4] px-3 py-1 text-xs"
                    >
                      {skill}
                    </span>
                  ))
                : '-'}
            </div>
          </section>

          {/* Job Simulation */}
          {/* <section className="flex items-start gap-4">
            <Image
              src="/job/job-simulation.png"
              alt={'Job Simulation'}
              width={297}
              height={181}
              className="rounded-md object-contain"
              loader={({ src }) => src}
            />

            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-800">
                Job Simulation: Junior Data Analyst Challenge
              </h3>
              <p className="mt-2 text-sm leading-relaxed text-gray-600">
                Before moving to the interview stage, you&apos;ll need to
                complete our Data Analyst Job Simulation – a short interactive
                task designed to test your practical skills in data cleaning,
                analysis, visualization, and interpretation. This helps us
                understand how you think with data and solve problems in a
                real-world context.
              </p>
              <Button className="mt-4 bg-primary text-white">
                Start Simulation Now
              </Button>
            </div>
          </section> */}
        </div>
      </div>
    </div>
  );
}
