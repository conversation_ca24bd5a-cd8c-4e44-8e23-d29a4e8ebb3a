'use client';

import { useMemo, useState } from 'react';
import dynamic from 'next/dynamic';
import FieldLabel from './field-label';
import { Input } from 'rizzui';
import CloseIcon from '../icons/close';
import { Controller, useFormContext } from 'react-hook-form';
import { FormValues } from '.';
import ReactDatePicker from '../date-picker';
import { cleanQuillContent } from '@/utils/quill-editor';

export default function StepTwo() {
  const QuillEditor = useMemo(
    () =>
      dynamic(() => import('@/views/quill-editor'), {
        ssr: false,
      }),
    []
  );

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid, isSubmitting },
  } = useFormContext<FormValues>();

  const [skill, setSkill] = useState<string>('');

  const skills = watch('skills') || [];

  return (
    <div className="space-y-4">
      <div>
        <div className="text-lg">
          Add detailed job description, responsibilities and terms
        </div>
        <div className="text-sm text-gray-500">
          Please fill in basic information for your job
        </div>
      </div>

      <div>
        <FieldLabel title="Add job description" />
        <Controller
          name="description"
          control={control}
          rules={{
            required: false,
          }}
          render={({ field }) => (
            <QuillEditor
              {...field}
              value={field.value || ''}
              onChange={(val) => {
                const cleanedContent = cleanQuillContent(val);
                field.onChange(cleanedContent);
              }}
              containerOptions={[['link']]}
              className="[&>.ql-container_.ql-editor]:min-h-[100px]"
            />
          )}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <div>
          <FieldLabel title="Add skills" content='Press Enter to add skill' />
          <Controller
            name="skills"
            control={control}
            rules={{
              required: false,
            }}
            render={({ field }) => (
              <Input
                value={skill}
                onChange={(e) => setSkill(e.target.value)}
                variant="flat"
                placeholder="Enter a Job title (e.g. Software Developer)"
                className="w-full rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0"
                inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && skill.trim()) {
                    e.preventDefault();
                    field.onChange([...field.value, skill.trim()]);
                    setSkill('');
                  }
                }}
              />
            )}
          />
          <div className="mt-3 flex flex-wrap gap-3">
            {skills.map((s, index) => (
              <span
                key={index}
                className="inline-flex h-8 items-center gap-1 rounded-full px-3 text-sm shadow-[0_4px_30px_rgba(0,0,0,0.15)]"
              >
                <button
                  type="button"
                  onClick={() =>
                    setValue(
                      'skills',
                      skills.filter((_, i) => i !== index)
                    )
                  }
                  className="rounded-full border border-[#222222] text-gray-500 hover:bg-gray-200 hover:text-gray-700"
                >
                  <CloseIcon className="h-3 w-3" />
                </button>
                {s}
              </span>
            ))}
          </div>
        </div>

        <div className="w-full">
          <FieldLabel title="Expires At" />
          <Controller
            name="expiresAt"
            control={control}
            rules={{
              required: false,
            }}
            render={({ field }) => (
              <ReactDatePicker
                selected={field.value}
                onChange={(date) => field.onChange(date)}
                placeholderText="Select Date"
                className="w-full rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0 [&_.react-datepicker-wrapper]:!w-full"
                isClearable
                minDate={new Date()}
              />
            )}
          />
        </div>
      </div>

      <div>
        <FieldLabel title="Job Simulation" />
        <div className="text-sm text-gray-500">
          Job Simulation will be automatically generated based on the job
          description and requirements you create.
        </div>
      </div>
    </div>
  );
}
