'use client';

import Image from 'next/image';
import MoneyIcon from '@/views/icons/money';
import ClockIcon from '@/views/icons/clock';
import TechnologyIcon from '@/views/icons/technology';
import HeartOutlineIcon from '@/views/icons/heart-outline';
import {
  getJobTypeString,
  levelStyle,
  simulationLevel,
} from '@/views/job/job-list';
import { Job } from '@/api-requests/job';
import cn from '@/utils/class-names';
import { safeFormatDistanceToNow } from '@/utils/date';

interface JobCardProps {
  job: Job;
  setSelectedJob: (job: Job) => void;
  selectedJob?: Job | null;
}

export default function JobCard({
  job,
  setSelectedJob,
  selectedJob,
}: JobCardProps) {
  const formattedDate = safeFormatDistanceToNow(new Date(job?.postedTime), {
    addSuffix: true,
  });

  const jobType = getJobTypeString(job);

  return (
    <div
      className={cn(
        'flex h-full flex-col justify-between rounded-[16px] border-[1px] border-[#E8E8E8] bg-white p-4',
        job?.jobId === selectedJob?.jobId && 'border-[2px] border-primary'
      )}
      onClick={() => setSelectedJob(job)}
    >
      <div>
        {/* Title + Badge */}
        <div className="flex items-start justify-between gap-1">
          <div>
            <h2 className="text-md line-clamp-2 flex-1 font-semibold text-gray-800">
              {job?.title}
            </h2>
            {job.simulation?.id && (
              <span className="rounded-full border border-primary bg-[#CCFFE7] px-3 py-0.5 text-[10px] font-medium text-primary">
                Simulation Available
              </span>
            )}
          </div>
          <div className="flex-0">
            <button
              className="group flex h-8 w-8 items-center justify-center rounded-[8px] bg-[#F9F9F9] hover:bg-primary hover:text-white"
              onClick={(e) => e.stopPropagation()}
            >
              <HeartOutlineIcon className="h-5 w-5 group-hover:text-white" />
            </button>
          </div>
        </div>

        <div className="mt-3 border-t border-gray-200"></div>

        {/* Company Info */}
        <div className="mt-3 flex items-center gap-3">
          {job?.companyLogoUrl ? (
            <Image
              src={job.companyLogoUrl}
              alt={job.companyName || 'Company Logo'}
              width={60}
              height={60}
              className="h-15 w-15 rounded-full object-contain"
              loader={({ src }) => src}
            />
          ) : (
            <div
              className="!h-15 !w-15 rounded-full bg-gray-100"
              style={{ width: '60px', height: '60px' }}
            />
          )}
          <div>
            <p className="text-sm font-medium text-gray-700">
              {job?.companyName}
            </p>
            <p className="text-[12px] text-gray-500">
              {job?.location} • {formattedDate}
            </p>
          </div>
        </div>

        {/* Job Details */}
        <div className="mt-4 flex flex-wrap items-center gap-2 text-[10px] text-gray-500">
          <span className="flex items-center gap-1">
            <MoneyIcon className="h-3 w-3" />
            <span>
              {typeof job?.salary === 'string'
                ? job.salary
                : [job.salary?.min, job.salary?.max].join(' - ') || '-'}
            </span>
          </span>
          <span className="flex items-center gap-1">
            <ClockIcon className="h-3 w-3" />
            <span>{jobType || '-'}</span>
          </span>
          <span className="flex items-center gap-1">
            <TechnologyIcon className="h-3 w-3" />
            {Array.isArray(job?.categories) && job?.categories?.length > 0 ? (
              <span>{job.categories.join(', ')}</span>
            ) : (
              '-'
            )}
          </span>
          {job?.simulation?.level && (
            <span
              className={`${levelStyle[simulationLevel[Number(job.simulation.level)]]} whitespace-nowrap rounded-full px-1`}
            >
              {simulationLevel[Number(job.simulation.level)]}:{' '}
              <b>{job.simulation.minute} mins</b>
            </span>
          )}
        </div>
      </div>

      <p
        className="mt-3 line-clamp-2 text-sm text-gray-600"
        dangerouslySetInnerHTML={{ __html: job.description || '' }}
      ></p>

      {/* Footer */}
      {/* <div>
        <div className="mt-3 border-t border-gray-200"></div>
        <div className="mt-4 flex items-center gap-4">
          <Button
            rounded="lg"
            className={cn(
              job?.jobId === selectedJob?.jobId
                ? 'bg-primary text-white'
                : 'border border-primary bg-white text-primary hover:bg-primary hover:text-white'
            )}
            size="sm"
            onClick={() => setSelectedJob(job)}
          >
            View detail
          </Button>
          <Button
            variant="outline"
            rounded="lg"
            className="flex items-center gap-1"
            size="sm"
          >
            <HeartOutlineIcon className="h-4 w-4" /> <span>Save</span>
          </Button>
          <div className="text-[12px] text-gray-500">
            <b>{job?.applicants}</b> people applied
          </div>
        </div>
      </div> */}
    </div>
  );
}
