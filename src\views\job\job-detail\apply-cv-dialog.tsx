'use client';

import {
  ProcessCVToApplyResponse,
  useApplyCV,
  useProcessCVToApply,
} from '@/api-requests/job/process-cv-to-apply';
import StartSimulationButton from '@/components/StartSimulationButton';
import { userAtom } from '@/store/user-atom';
import { getUserError } from '@/utils/api-error';
import cn from '@/utils/class-names';
import { useAtom } from 'jotai';
import { ChevronDown, Sparkles } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import { Accordion, Button, Modal, Progressbar } from 'rizzui';

const DELAY_PROGRESS = 1700;

interface IProps {
  jobId: string;
  simulationId: string;
  open: boolean;
  setOpen: (open: boolean) => void;
  onApplySuccess: () => void;
  onErrorJobApplied?: () => void;
}

const ApplyCVStep1 = ({
  jobId,
  simulationId,
  onSuccess,
  onErrorJobApplied,
}: {
  jobId: string;
  simulationId: string;
  onSuccess: (result: ProcessCVToApplyResponse | null) => void;
  onErrorJobApplied?: () => void;
}) => {
  const [progress, setProgress] = useState(0);
  const hasCalledRef = useRef(false);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const { mutateAsync: processCV } = useProcessCVToApply();
  const [showRetryButton, setShowRetryButton] = useState(false);

  // useEffect(() => {
  //   const duration = 6000;
  //   const target = 90;
  //   const stepTime = 100; // ms
  //   const step = (target / duration) * stepTime;

  //   progressIntervalRef.current = setInterval(() => {
  //     setProgress((prev) => {
  //       if (prev + step >= target) {
  //         if (progressIntervalRef.current)
  //           clearInterval(progressIntervalRef.current);
  //         return target;
  //       }
  //       return prev + step;
  //     });
  //   }, stepTime);

  //   return () => {
  //     if (progressIntervalRef.current) {
  //       clearInterval(progressIntervalRef.current);
  //       progressIntervalRef.current = null;
  //     }
  //   };
  // }, []);

  const startProgress = () => {
    const duration = 6000;
    const target = 90;
    const stepTime = 100; // ms
    const step = (target / duration) * stepTime;
    setProgress(0);

    progressIntervalRef.current = setInterval(() => {
      setProgress((prev) => {
        if (prev + step >= target) {
          if (progressIntervalRef.current)
            clearInterval(progressIntervalRef.current);
          return target;
        }
        return prev + step;
      });
    }, stepTime);
  };

  const startProcessingCV = async () => {
    startProgress();
    setShowRetryButton(false);
    try {
      const res = await processCV({ jobId, simulationId });
      setTimeout(() => {
        onSuccess?.(res);
      }, DELAY_PROGRESS);
    } catch (error: any) {
      const userError = getUserError(
        error,
        'Failed to process CV. Please try again later.'
      );
      setTimeout(() => {
        userError.code === 'JOB_APPLIED'
          ? toast.success(userError.message)
          : toast.error(userError.message);
        if (userError.code === 'JOB_APPLIED' && !!onErrorJobApplied) {
          onErrorJobApplied?.();
        } else setShowRetryButton(true);
      }, DELAY_PROGRESS);
    }

    setTimeout(() => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
      setProgress(100);
    }, 1500);
  };

  useEffect(() => {
    if (hasCalledRef.current) return;

    hasCalledRef.current = true;
    startProcessingCV();
  }, []);

  return (
    <div className="flex flex-col items-center gap-5 p-6 text-center">
      <img src="/job/cv-edit.png" alt="" className="h-auto w-36" />
      <p className="text-xl font-bold text-[#484848]">Processing your CV...</p>
      <div className="text-[16px]">
        {!showRetryButton ? (
          <>
            <p>
              The system is analyzing the CV and job simulation information.
            </p>
            <p>Please wait a moment ...</p>
          </>
        ) : (
          <>
            <p>The CV processing failed. Please try again.</p>
          </>
        )}
      </div>
      <div className="w-full">
        {!showRetryButton ? (
          <>
            <Progressbar
              value={progress}
              trackClassName="bg-gray-200 [&>div]:transition-all [&>div]:duration-100"
              labelClassName="hidden"
            />
            <div className="flex flex-row">
              <span className="ml-auto">{Math.round(progress)}%</span>
            </div>
          </>
        ) : (
          <Button className="text-white" onClick={startProcessingCV}>
            Try again
          </Button>
        )}
      </div>
    </div>
  );
};

const ApplyCVStep2 = ({
  result,
  simulationId,
  onClose,
  onSuccess,
  onErrorJobApplied,
}: {
  result: ProcessCVToApplyResponse;
  simulationId: string;
  onClose: () => void;
  onSuccess: () => void;
  onErrorJobApplied?: () => void;
}) => {
  // const isFailed = result.status === 'failed';

  const aiOverview = [...(result.overview || []), ...(result.feedback || [])];

  const [user] = useAtom(userAtom);

  const { mutateAsync: applyCV, isPending } = useApplyCV();

  const handleApplyCV = async () => {
    if (!result) return;

    try {
      await applyCV({ processId: result.id });
      toast.success('Successfully applied for the job.');
      onSuccess?.();
      onClose?.();
    } catch (error: any) {
      const userError = getUserError(
        error,
        'Failed to apply for the job. Please try again later.'
      );
      toast.error(userError.message);

      if (userError.code === 'JOB_APPLIED') {
        onErrorJobApplied?.();
      }
    }
  };

  return (
    <div className="flex flex-col items-center gap-6 p-8 text-center">
      {/* Top icon */}
      <div className="flex items-center justify-center">
        <img
          className="w-[90px]"
          // src={isFailed ? '/circle-x.png' : '/circle-check.png'}
          src={'/circle-check.png'}
        />
      </div>

      {/* Title */}
      <p className="text-2xl font-bold text-[#1B1C1E]">
        {/* {isFailed ? 'Application Not Successful' : 'Congratulations!'} */}
        CV Match Result
      </p>

      {/* Subtitle */}
      <p className="text-[16px] text-[#5E5E5E]">
        We analyzed your CV against the job requirements. Here’s how well your
        profile aligns with this role.
      </p>

      {/* Candidate card (pure Tailwind) */}
      <div className="w-full overflow-hidden rounded-2xl border border-gray-200 bg-white text-left shadow">
        {/* Header row */}
        <div className="flex flex-col gap-2 border-b border-gray-200 p-5 md:flex-row md:items-center md:justify-between">
          <div>
            <p className="text-lg font-bold text-[#1B1C1E]">
              {user?.firstName} {user?.lastName}
            </p>
            <p className="text-sm">
              <span className="text-gray-500">Position:</span> {result.position}
            </p>
            {/* <p className="text-sm">
              <span className="text-gray-500">Status:</span>{' '}
              <span
                className={`font-semibold ${
                  isFailed ? 'text-red-600' : 'text-green-600'
                }`}
              >
                {isFailed ? 'Fail' : 'Pass'}
              </span>
            </p> */}
          </div>
          <div className="flex flex-col items-center justify-center text-right">
            <p className="text-sm text-gray-500">Match</p>
            <p className="text-2xl font-bold text-[#1B1C1E]">
              {result.matchPercentage}%
            </p>
          </div>
        </div>

        {/* Accordions */}
        <div className="p-5">
          {/* AI Overview */}
          {aiOverview.length > 0 && (
            <Accordion
              className={cn(
                'py-3',
                !!result.tasks?.length && 'border-b border-gray-300'
              )}
              defaultOpen
            >
              <Accordion.Header>
                {({ open }) => (
                  <div className="flex w-full cursor-pointer items-center">
                    <div className="flex items-center gap-2">
                      <Sparkles className="h-4 w-4" />
                      <span className="text-[15px] font-medium">
                        AI Overview
                      </span>
                    </div>
                    <ChevronDown
                      className={`ml-auto h-4 w-4 -rotate-90 transform transition-transform duration-300 ${
                        open ? 'rotate-0' : ''
                      }`}
                    />
                  </div>
                )}
              </Accordion.Header>
              <Accordion.Body>
                <ul className="ml-4 mt-4 list-disc space-y-1 text-sm text-gray-700">
                  {aiOverview.map((item, idx) => (
                    <li key={idx}>{item}</li>
                  ))}
                </ul>
              </Accordion.Body>
            </Accordion>
          )}

          {/* TASKS */}
          {(result.tasks || [])?.length > 0 && (
            <Accordion className="pt-2">
              <Accordion.Header>
                {({ open }) => (
                  <div className="flex w-full cursor-pointer items-center">
                    <div className="flex items-center gap-2">
                      {/* <Sparkles className="h-4 w-4" /> */}
                      <span className="text-[15px] font-medium">Tasks</span>
                    </div>
                    <ChevronDown
                      className={`ml-auto h-4 w-4 -rotate-90 transform transition-transform duration-300 ${
                        open ? 'rotate-0' : ''
                      }`}
                    />
                  </div>
                )}
              </Accordion.Header>
              <Accordion.Body>
                <ul className="ml-4 mt-4 list-decimal space-y-3 text-sm text-gray-700">
                  {result.tasks.map((task, idx) => (
                    <li key={idx}>
                      <p className="font-semibold">{task.title}</p>
                      <p>
                        <span className="font-semibold">Description:</span>{' '}
                        {task.description}
                      </p>
                      {/* <pre className="whitespace-pre-wrap">
                      {task.exampleSubmission}
                    </pre> */}
                      <div>
                        <p className="font-semibold">Example Submission:</p>
                        <p className="whitespace-pre-wrap rounded bg-gray-100 p-2">
                          {task.exampleSubmission}
                        </p>
                      </div>
                    </li>
                  ))}
                </ul>
              </Accordion.Body>
            </Accordion>
          )}
        </div>
      </div>

      <p className="text-xs">
        * We encourage you to take the job simulation to demonstrate your true
        capabilities and boost your chances of success.
      </p>

      <div className="flex flex-row gap-3">
        <Button
          variant="outline"
          onClick={() => onClose?.()}
          disabled={isPending}
        >
          Cancel
        </Button>
        <StartSimulationButton
          simId={simulationId}
          buttonProps={{
            variant: 'solid',
            className: 'bg-[#0D1321] text-white',
            disabled: isPending,
          }}
        >
          Try the Job Simulation
        </StartSimulationButton>
        <Button
          variant="solid"
          className="bg-[#0D1321] text-white hover:bg-[#0b0f1b]"
          disabled={!result || isPending}
          onClick={handleApplyCV}
        >
          Continue
        </Button>
      </div>
    </div>
  );
};

export default function ApplyCVModal({
  open,
  jobId,
  simulationId,
  setOpen,
  onApplySuccess,
  onErrorJobApplied,
}: IProps) {
  const [step, setStep] = useState<number>(1);
  const [result, setResult] = useState<ProcessCVToApplyResponse | null>(null);
  // const router = useRouter();

  // const [user] = useAtom(userAtom);

  const handleProcessCVSuccess = (result: ProcessCVToApplyResponse | null) => {
    setResult(result);
    setStep(2);
    // Handle success
  };

  const renderStep = () => {
    switch (step) {
      case 1:
        return (
          <ApplyCVStep1
            jobId={jobId}
            simulationId={simulationId}
            onSuccess={handleProcessCVSuccess}
            onErrorJobApplied={onErrorJobApplied}
          />
        );
      case 2:
        return result ? (
          <ApplyCVStep2
            result={result}
            simulationId={simulationId}
            onClose={() => setOpen(false)}
            onSuccess={onApplySuccess}
            onErrorJobApplied={onErrorJobApplied}
          />
        ) : (
          <>No Result</>
        );
      default:
        return null;
    }
  };

  return (
    <Modal
      isOpen={open}
      onClose={() => setOpen(false)}
      containerClassName={
        step === 1
          ? 'md:w-[600px] md:max-w-[600px]'
          : 'md:w-[1080px] md:max-w-[1080px]'
      }
    >
      <div className="p-2">{renderStep()}</div>
    </Modal>
  );
}
