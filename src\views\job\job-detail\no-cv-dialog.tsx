'use client';

import { user<PERSON>tom } from '@/store/user-atom';
import { useAtom } from 'jotai';
import { ArrowRight } from 'lucide-react';
import { Button, Modal } from 'rizzui';

interface IProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export default function NoCVModal({ open, setOpen }: IProps) {
  const [user] = useAtom(userAtom);

  const handleGotoProfileCV = () => {
    if (!user) return;
    window.open(`/profile/${user.id}#cv`, '_blank');
  };

  return (
    <Modal isOpen={open} onClose={() => setOpen(false)}>
      <div className="flex flex-col items-center gap-5 p-6 text-center">
        <img src="/job/cv-edit.png" alt="" className="h-auto w-36" />
        <p className="text-xl font-bold text-[#484848]">
          You haven't created your CV yet
        </p>
        <p className="text-[16px]">
          Start building your professional CV to increase your chances of
          getting hired.
        </p>
        <div className="">
          <Button
            onClick={handleGotoProfileCV}
            variant="solid"
            className="text-white"
          >
            <span>Create CV Now</span>
            <ArrowRight size={15} className="ml-2" />
          </Button>
        </div>
      </div>
    </Modal>
  );
}
