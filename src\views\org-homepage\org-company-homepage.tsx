'use client';

import {
  useGetOrgFeaturedSimulations,
  useGetOrgHomeJobsInfinite,
} from '@/api-requests/job/get-org-home-jobs';
import { orgAtom } from '@/store/organization-atom';
import { useAtom } from 'jotai';
import { useEffect, useRef, useState } from 'react';
import OrgHomepageJobCard from './job/job-card';

const JobList = () => {
  const loaderRef = useRef<HTMLDivElement | null>(null);
  const [keyword, _setKeyword] = useState('');

  const [org] = useAtom(orgAtom);

  const { jobs: featuredJobs } = useGetOrgFeaturedSimulations({
    orgId: org?._id || '',
  });

  const {
    jobs,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    updateJobStatus,
  } = useGetOrgHomeJobsInfinite({ orgId: org?._id || '', keyword });

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting) {
          console.log('Loader is intersecting');
        }
        if (
          target.isIntersecting &&
          !isFetchingNextPage &&
          org?._id &&
          hasNextPage
        ) {
          fetchNextPage();
        }
      },
      {
        root: null,
        rootMargin: '0px',
        threshold: 1.0,
      }
    );

    if (loaderRef.current) {
      observer.observe(loaderRef.current);
    }

    return () => {
      if (loaderRef.current) {
        observer.unobserve(loaderRef.current);
      }
    };
  }, [hasNextPage, isFetchingNextPage, org?._id]);

  return (
    <div className="space-y-10">
      <div className="">
        <h2 className="mb-4 text-sm font-semibold">Featured Jobs</h2>

        {!featuredJobs?.length ? (
          <div className="text-center text-gray-500">No jobs found.</div>
        ) : (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {featuredJobs.map((job) => (
              <OrgHomepageJobCard
                key={job.jobId}
                job={job}
                onApplyCVSuccess={() => {
                  updateJobStatus(job.jobId, 'completed');
                }}
                onErrorJobApplied={() => {
                  updateJobStatus(job.jobId, 'completed');
                }}
              />
            ))}
          </div>
        )}
      </div>
      <div className="">
        <h2 className="mb-4 text-sm font-semibold">Job List</h2>

        {!jobs?.length ? (
          <div className="text-center text-gray-500">No jobs found.</div>
        ) : (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {jobs.map((job) => (
              <OrgHomepageJobCard
                key={job.jobId}
                job={job}
                onApplyCVSuccess={() => {
                  updateJobStatus(job.jobId, 'completed');
                }}
                onErrorJobApplied={() => {
                  updateJobStatus(job.jobId, 'completed');
                }}
              />
            ))}
          </div>
        )}

        {/* Sentinel element */}
        <div ref={loaderRef} className="flex h-12 items-center justify-center">
          {isFetchingNextPage && (
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-gray-300 border-t-blue-500"></div>
          )}
        </div>
      </div>
    </div>
  );
};

export default function UserOrgCompanyHomepage() {
  return <JobList />;
}
