'use client';

import { <PERSON><PERSON>, Button } from 'rizzui';
import { ProfileInfo } from '../user/profile-detail';
import { UserProfile } from '@/api-requests/user-profile';
import { UserInfo } from '@/store/user-atom';

interface IProps {
  profile: UserProfile | null;
  user: UserInfo | null;
  onEditProfile?: () => void;
}
export default function EmployerInfo({ profile, user, onEditProfile }: IProps) {
  const personalDetails = [
    { label: 'First name', value: user?.firstName || '-' },
    { label: 'Last name', value: user?.lastName || '-' },
    { label: 'Gender', value: profile?.gender || '-' },
    { label: 'Address', value: profile?.address || '-' },
    { label: 'City', value: profile?.city || '-' },
    { label: 'Region', value: profile?.region || '-' },
    { label: 'Country', value: profile?.country || '-' },
  ];

  return (
    <div className="space-y-6">
      <h2 className="text-lg font-semibold text-gray-900">Personal details</h2>
      <div className="grid grid-cols-1 items-start gap-6 sm:grid-cols-2 md:grid-cols-3">
        <div className="flex flex-col items-start">
          <div className="mb-2 text-sm text-gray-500">Your Avatar</div>
          <Avatar src="" name="Brooklyn Simmons" customSize={100} />
        </div>

        <div className="grid grid-cols-1 gap-3 sm:col-span-1 sm:grid-cols-2 md:col-span-2">
          {personalDetails.map((detail) => (
            <ProfileInfo
              key={detail.label}
              label={detail.label}
              value={detail.value}
            />
          ))}
        </div>
      </div>
      <div className="flex justify-end">
        <Button className="bg-primary text-white" onClick={onEditProfile}>
          Edit Profile
        </Button>
      </div>
    </div>
  );
}
