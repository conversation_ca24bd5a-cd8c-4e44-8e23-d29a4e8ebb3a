'use client';

import { useEffect, useMemo, useState } from 'react';
import {
  Modal,
  Input,
  Select,
  ActionIcon,
  Title,
  Button,
  Text,
  Avatar,
} from 'rizzui';
import countriesData from '@/data/countries.json';
import FieldLabel from '@/views/job-creation/field-label';
import CloseIcon from '@/views/icons/close';
import { Controller, useForm } from 'react-hook-form';
import ImageUploader from '@/views/image-uploader';
import UploadIcon from '@/views/icons/upload';
import Image from 'next/image';
import DeleteIcon from '@/views/icons/delete';
import {
  UpdateUserProfileParams,
  UserProfile,
  useUpdateUserProfile,
} from '@/api-requests/user-profile';
import toast from 'react-hot-toast';
import { updateUserAtom, userAtom, UserInfo } from '@/store/user-atom';
import { useSetAtom } from 'jotai';

type FormValues = {
  firstName?: string;
  lastName?: string;
  gender?: {
    label: string;
    value: string;
  } | null;
  address?: string;
  city?: string;
  region?: string;
  country: {
    label: string;
    value: string;
  } | null;
  file?: File | null;
};

interface IProps {
  open: boolean;
  onClose: () => void;
  refetch?: () => void;
  profile: UserProfile | null;
  user: UserInfo | null;
}

interface Option {
  label: string;
  value: string;
}

const genderOptions: Option[] = [
  { label: 'Male', value: 'male' },
  { label: 'Female', value: 'female' },
];

export default function ProfileDetailModal({
  open,
  onClose,
  refetch,
  profile,
  user,
}: IProps) {
  const updateUser = useSetAtom(updateUserAtom);
  const [error, setError] = useState<string>('');

  const countryOptions = useMemo(
    () =>
      countriesData.map((country) => ({
        label: country.name,
        value: country.code,
      })),
    []
  );

  const { mutateAsync } = useUpdateUserProfile();

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid, isSubmitting },
  } = useForm<FormValues>({
    mode: 'onChange',
    defaultValues: {
      firstName: profile?.firstName || user?.firstName || '',
      lastName: profile?.lastName || user?.lastName || '',
      gender: genderOptions.find((g) => g.value === profile?.gender) || null,
      address: profile?.address || '',
      city: profile?.city || '',
      region: profile?.region || '',
      country: countryOptions.find((c) => c.label === profile?.country) || null,
      file: null,
    },
  });

  const file = watch('file');

  const preview = useMemo(
    () => (file ? URL.createObjectURL(file) : profile?.avatar || ''),
    [file]
  );
  useEffect(() => {
    return () => {
      if (preview) URL.revokeObjectURL(preview);
    };
  }, [preview]);

  const onSubmit = async (data: FormValues) => {
    if (!isValid) return;

    const resp = await mutateAsync({
      ...data,
      userId: user?.id,
      country: data.country?.label,
      gender: data.gender?.value,
    } as UpdateUserProfileParams);

    if (resp) {
      setError('');
      onClose();
      refetch?.();
      updateUser({
        firstName: resp.firstName,
        lastName: resp.lastName,
        avatar: resp.avatar,
      });
    } else {
      toast.error('Failed to update profile');
    }
  };

  return (
    <Modal isOpen={open} onClose={onClose} size="lg">
      <div
        className="scrollbar-hide max-h-[80vh] w-full overflow-y-auto rounded-[20px] p-6 md:w-[640px]"
        style={{
          msOverflowStyle: 'none',
          scrollbarWidth: 'none',
        }}
      >
        <div className="mb-7 flex items-center justify-between">
          <Title as="h3">Edit Profile</Title>
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>

        <div className="space-y-4">
          <div>
            <FieldLabel title="Avatar" />
            <ImageUploader
              onChange={(f: File | null) => {
                setError('');
                setValue('file', f);
              }}
              onError={(msg) => setError(msg)}
            >
              {({ getRootProps, dragActive }) => (
                <div
                  {...getRootProps()}
                  className={[
                    'relative flex h-[100px] w-[100px] cursor-pointer items-center justify-center rounded-full border bg-[#F4F4F4] transition',
                    dragActive
                      ? 'border-gray-400'
                      : 'border-gray-200 hover:border-gray-300',
                  ].join(' ')}
                  role="button"
                  aria-label="Upload image"
                >
                  {!preview ? (
                    <div className="flex flex-col items-center text-gray-400">
                      <UploadIcon className="h-10 w-10" />
                    </div>
                  ) : (
                    <>
                      <Avatar
                        src={preview}
                        name={user?.firstName + ' ' + user?.lastName}
                        customSize={100}
                      />

                      <ActionIcon
                        variant="outline"
                        size="sm"
                        rounded="full"
                        className="group absolute bottom-1 left-1/2 h-6 w-6 -translate-x-1/2 bg-gray-200 hover:bg-primary hover:text-primary"
                        onClick={(e) => {
                          e.stopPropagation();
                          setValue('file', null);
                        }}
                      >
                        <DeleteIcon className="h-3 w-3 group-hover:text-white" />
                      </ActionIcon>
                    </>
                  )}
                </div>
              )}
            </ImageUploader>
            {error && (
              <Text as="p" className="mt-0.5 text-xs text-red-600">
                {error}
              </Text>
            )}
          </div>

          <div className="grid grid-cols-1 gap-4 sm:col-span-1 md:col-span-2 md:grid-cols-2">
            <div>
              <FieldLabel title="First name" />
              <Controller
                name="firstName"
                control={control}
                rules={{
                  required: 'Please enter first name',
                  maxLength: {
                    value: 24,
                    message: 'First name cannot exceed 24 characters',
                  },
                }}
                render={({ field }) => (
                  <Input
                    {...field}
                    variant="flat"
                    placeholder="Enter your full name"
                    className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
                    inputClassName="!border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                  />
                )}
              />
              {errors.firstName && (
                <Text as="p" className="mt-0.5 text-xs text-red-600">
                  {errors.firstName.message}
                </Text>
              )}
            </div>
            <div>
              <FieldLabel title="Last name" />
              <Controller
                name="lastName"
                control={control}
                rules={{
                  required: 'Please enter last name',
                  maxLength: {
                    value: 24,
                    message: 'First name cannot exceed 24 characters',
                  },
                }}
                render={({ field }) => (
                  <Input
                    {...field}
                    variant="flat"
                    placeholder="Enter your last name"
                    className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
                    inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                  />
                )}
              />
              {errors.lastName && (
                <Text as="p" className="mt-0.5 text-xs text-red-600">
                  {errors.lastName.message}
                </Text>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:col-span-1 md:col-span-2 md:grid-cols-2">
            <div>
              <FieldLabel title="Gender" />
              <Controller
                name="gender"
                control={control}
                rules={{
                  required: false,
                }}
                render={({ field }) => (
                  <Select
                    {...field}
                    clearable
                    onClear={() => field.onChange(null)}
                    options={genderOptions}
                    placeholder="Select gender"
                    className="w-full [&_.rizzui-select-button]:!rounded-lg [&_.rizzui-select-button]:!border-0 [&_.rizzui-select-button]:!bg-[#F4F4F4] [&_.rizzui-select-button]:!px-4 [&_.rizzui-select-button]:!outline-0 [&_.rizzui-select-button]:!ring-0 [&_.rizzui-select-button]:focus:!ring-0"
                  />
                )}
              />
            </div>

            <div>
              <FieldLabel title="Address" />
              <Controller
                name="address"
                control={control}
                rules={{
                  required: false,
                }}
                render={({ field }) => (
                  <Input
                    {...field}
                    variant="flat"
                    placeholder="Enter your address"
                    className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
                    inputClassName="!border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                  />
                )}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:col-span-1 md:col-span-2 md:grid-cols-2">
            <div>
              <FieldLabel title="City" />
              <Controller
                name="city"
                control={control}
                rules={{
                  required: false,
                }}
                render={({ field }) => (
                  <Input
                    {...field}
                    variant="flat"
                    placeholder="Enter your city"
                    className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
                    inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                  />
                )}
              />
            </div>

            <div>
              <FieldLabel title="Region" />
              <Controller
                name="region"
                control={control}
                rules={{
                  required: false,
                }}
                render={({ field }) => (
                  <Input
                    {...field}
                    variant="flat"
                    placeholder="Enter your region"
                    className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
                    inputClassName="!border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                  />
                )}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:col-span-1 md:col-span-2 md:grid-cols-2">
            <div>
              <FieldLabel title="Country" />
              <Controller
                name="country"
                control={control}
                rules={{
                  required: 'Please select your country',
                }}
                render={({ field }) => (
                  <Select
                    {...field}
                    clearable
                    onClear={() => field.onChange(null)}
                    options={countryOptions}
                    placeholder="Select your country"
                    className="w-full [&_.rizzui-select-button]:!rounded-lg [&_.rizzui-select-button]:!border-0 [&_.rizzui-select-button]:!bg-[#F4F4F4] [&_.rizzui-select-button]:!px-4 [&_.rizzui-select-button]:!outline-0 [&_.rizzui-select-button]:!ring-0 [&_.rizzui-select-button]:focus:!ring-0"
                    searchable={true}
                  />
                )}
              />
              {errors.country && (
                <Text as="p" className="mt-0.5 text-xs text-red-600">
                  {errors.country.message}
                </Text>
              )}
            </div>
          </div>
        </div>

        <div className="mt-6 flex justify-end gap-3">
          <Button variant="outline" className="border-primary">
            Cancel
          </Button>
          <Button
            className="bg-primary text-white"
            onClick={handleSubmit(onSubmit)}
            disabled={
              !user || isSubmitting || !isValid || user.id !== profile?.userId
            }
            isLoading={isSubmitting}
          >
            Save
          </Button>
        </div>
      </div>
    </Modal>
  );
}
