'use client';

import { useState } from 'react';
import { Button } from 'rizzui';
import ProfileDetailModal from './profile-detail-modal';
import { UserProfile } from '@/api-requests/user-profile';
import { useAtom } from 'jotai';
import { userAtom } from '@/store/user-atom';

interface IProps {
  refetch?: () => void;
  profile: UserProfile | null;
}

export const ProfileInfo = ({
  label,
  value,
}: {
  label: string;
  value: any;
}) => {
  return (
    <div className="">
      <div className="text-sm text-gray-500">{label}</div>
      <div>{!!value ? value : '-'}</div>
    </div>
  );
};

const getGenderDisplay = (gender?: string | null) => {
  if (!gender) return '-';
  switch (gender) {
    case 'male':
      return 'Male';
    case 'female':
      return 'Female';
    case 'other':
      return 'Other';
    default:
      return '';
  }
};

export default function ProfileDetail({ refetch, profile }: IProps) {
  const [openModal, setOpenModal] = useState(false);
  const [user] = useAtom(userAtom);

  return (
    <>
      <div className="w-full rounded-[20px] p-6 shadow-[0_4px_30px_rgba(0,0,0,0.15)] transition-all duration-300 ease-in-out">
        <div className="text-lg font-bold">Profile details</div>

        <div className="mt-4 grid grid-cols-1 gap-3 sm:grid-cols-2">
          <ProfileInfo label="First name" value={user?.firstName} />
          <ProfileInfo label="Last name" value={user?.lastName} />
          <ProfileInfo
            label="Gender"
            value={getGenderDisplay(profile?.gender) || ''}
          />

          <ProfileInfo label="Address" value={profile?.address} />
          <ProfileInfo label="City" value={profile?.city} />
          <ProfileInfo label="Region" value={profile?.region} />
          <ProfileInfo label="Country" value={profile?.country} />
        </div>

        {user && user?.id === profile?.userId && (
          <div className="mt-4 flex justify-end">
            <Button
              className="bg-primary text-white"
              onClick={() => setOpenModal(true)}
              disabled={!user || user?.id !== profile?.userId}
            >
              Edit Profile
            </Button>
          </div>
        )}
      </div>

      {openModal && (
        <ProfileDetailModal
          open={openModal}
          onClose={() => setOpenModal(false)}
          refetch={refetch}
          profile={profile}
          user={user}
        />
      )}
    </>
  );
}
