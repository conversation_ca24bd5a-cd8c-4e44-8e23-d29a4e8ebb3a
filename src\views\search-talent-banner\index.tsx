'use client';

import { Button, Input } from 'rizzui';
import SearchIcon from '../icons/search';
import { useAtom } from 'jotai';
import { searchTalentAtom } from '@/store/talent-atom';

export default function SearchTalentBanner() {
  const [, setSearchTalent] = useAtom(searchTalentAtom);
  return (
    <section
      className="bg-cover bg-center bg-no-repeat py-10"
      style={{ backgroundImage: 'url("/job/job-hero-bg.png")' }}
    >
      <div className="mx-auto w-full max-w-[1200px] px-4 xl:px-0">
        <h1 className="text-center text-xl font-bold leading-tight sm:text-2xl md:text-3xl">
          Find the Right Talent.
          <br />
          <span className="font-bold">Backed by Real Performance</span>
        </h1>

        <p className="sm:text-md mt-6 text-center text-base leading-relaxed">
          Search smarter with AI-powered suggestions and real-world job
          simulation results.
          <br />
          Simply enter a keyword, and let AI help you discover the most suitable
          candidates.
        </p>

        <Input
          variant="flat"
          prefix={<SearchIcon className="h-5 w-5 text-gray-500" />}
          suffix={
            <Button
              className="rounded-full bg-primary text-white"
              size="sm"
              onClick={() => setSearchTalent(true)}
            >
              search candidate
            </Button>
          }
          placeholder="Search by job title, key work..."
          className="mt-6 w-full min-w-[200px] rounded-full bg-white px-4 shadow-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0"
          inputClassName="!border-0 !shadow-none !outline-none !ring-0 focus:!ring-0 p-0 text-sm "
          size="lg"
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              setSearchTalent(true);
            }
          }}
        />
      </div>
    </section>
  );
}
