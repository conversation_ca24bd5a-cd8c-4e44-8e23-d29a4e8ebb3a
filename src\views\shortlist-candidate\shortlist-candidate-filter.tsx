'use client';

import {
  Input,
  InputProps,
  SelectProps,
  Select,
  Tooltip,
  ActionIcon,
} from 'rizzui';
import SearchIcon from '@/views/icons/search';
import { SelectOption } from '@/api-requests/types';
import DeleteIcon from '../icons/delete';
import { ShortlistCandidate } from '@/api-requests/shortlist-candidate';
import ExcelIcon from '../icons/excel';

const statusOptions: SelectOption[] = [
  { label: 'Active', value: 'active' },
  { label: 'Completed', value: 'completed' },
];

interface IProps {
  searchProps: InputProps & { ref?: React.RefObject<HTMLInputElement> };
  statusProps: {
    onClear: () => void;
    onChange: (option: SelectOption | null) => void;
  } & Omit<SelectProps<SelectOption>, 'options' | 'onChange'>;
  selectedCandidates: ShortlistCandidate[];
  totalCandidates: number;
  onDeleteClick: () => void;
  onExportToExcel: () => void;
}

export default function ShortListCandidateFilter({
  searchProps,
  statusProps,
  selectedCandidates,
  totalCandidates,
  onDeleteClick,
  onExportToExcel,
}: IProps) {
  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div className="flex w-full flex-nowrap items-center gap-3 sm:gap-4">
        <div className="">
          <Input
            variant="flat"
            prefix={<SearchIcon className="h-5 w-5 text-gray-400" />}
            placeholder="Search candidate name"
            className="w-full min-w-[200px] max-w-xs rounded-lg px-2 shadow-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:!shadow-none [&_*]:!outline-none [&_*]:!ring-0"
            inputClassName="!border-0 !shadow-none !outline-none !ring-0 focus:!ring-0 p-0 text-sm"
            size="sm"
            {...searchProps}
          />
        </div>

        <div className="flex items-center gap-2 whitespace-nowrap text-sm text-gray-500">
          <div className="text-gray-400">{totalCandidates} Results</div>
          <div className="h-4 w-px bg-gray-300" />
          <Tooltip color="invert" content="Export to Excel">
            <ActionIcon
              variant="text"
              size="sm"
              className="h-fit w-fit"
              onClick={onExportToExcel}
            >
              <ExcelIcon className="h-4 w-4 text-gray-500 hover:text-red-500" />
            </ActionIcon>
          </Tooltip>

          {selectedCandidates.length > 0 && (
            <>
              <div className="h-4 w-px bg-gray-300" />
              <div className="font-medium text-gray-900">
                {selectedCandidates.length} selected
              </div>
              <div className="h-4 w-px bg-gray-300" />
              <Tooltip color="invert" content="Remove selected candidates">
                <ActionIcon
                  variant="text"
                  size="sm"
                  className="h-fit w-fit"
                  onClick={onDeleteClick}
                >
                  <DeleteIcon className="h-4 w-4 text-gray-500 hover:text-red-500" />
                </ActionIcon>
              </Tooltip>
            </>
          )}
        </div>
      </div>

      <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
        {/* Search bar - full width on mobile, constrained on larger screens */}
        {/* <div className="w-full sm:w-auto">
          <Input
            variant="flat"
            prefix={<SearchIcon className="h-5 w-5 text-gray-400" />}
            placeholder="Search candidate name"
            className="w-full min-w-[200px] rounded-lg px-2 shadow-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:!shadow-none [&_*]:!outline-none [&_*]:!ring-0"
            inputClassName="!border-0 !shadow-none !outline-none !ring-0 focus:!ring-0 p-0 text-sm"
            size="sm"
            {...searchProps}
          />
        </div> */}

        {/* Filter container */}
        <div className="flex flex-nowrap items-center gap-3">
          <div className="whitespace-nowrap text-sm opacity-50">Filter by:</div>

          <Select
            clearable
            options={statusOptions}
            placeholder="Select status"
            className="w-full min-w-[120px] rounded-lg shadow-[0_1px_3px_0_rgba(0,0,0,0.08)] transition-all duration-300 ease-in-out hover:shadow-[0_4px_12px_0_rgba(0,0,0,0.15)] sm:w-auto [&>.rizzui-popover]:rounded-xl [&>.rizzui-popover]:shadow-md [&_.rizzui-select-button]:!border [&_.rizzui-select-button]:!border-gray-200"
            size="sm"
            {...statusProps}
          />
        </div>
      </div>
    </div>
  );
}
