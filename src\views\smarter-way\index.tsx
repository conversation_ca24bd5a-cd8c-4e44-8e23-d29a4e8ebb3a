'use client';

import Image from 'next/image';
import Link from 'next/link';
import { Button } from 'rizzui';

export default function SmarterWay() {
  return (
    <div className="flex flex-col items-center justify-between rounded-2xl bg-primary px-6 py-4 md:flex-row md:items-center md:px-12 md:py-0">
      <div className="text-center md:max-w-lg md:text-left">
        <h2 className="text-2xl font-semibold text-white md:text-3xl">
          A Smarter Way to Get Hired
        </h2>
        <p className="md:text-md mt-4 text-base text-white hover:shadow-xl">
          Stop scrolling. Our AI + simulations help you skip the noise and find
          the role made for you
        </p>
        <Link href="/find-jobs">
          <Button
            className="mt-6 rounded-lg bg-white text-black"
            size="lg"
          >
            Start Your Smart Job Search
          </Button>
        </Link>
      </div>

      <div className="mt-8 flex justify-center md:mt-0 md:justify-end">
        <Image
          src="/job/smarter-way.png"
          alt="Smarter Way"
          width={0}
          height={278}
          className="w-auto object-contain"
          loader={({ src }) => src}
        />
      </div>
    </div>
  );
}
