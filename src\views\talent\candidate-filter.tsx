'use client';

import { useState } from 'react';

import UpIcon from '@/views/icons/up';
import CloseIcon from '@/views/icons/close';
import cn from '@/utils/class-names';
import { Checkbox, Accordion, Input } from 'rizzui';

const filterOptions = {
  matchScore: [
    { label: '90–100% (Highly matched)', value: '90-100' },
    { label: '75–89% (Good match)', value: '75-89' },
    { label: '< 75% (Low match)', value: '<75' },
  ],
  simulation: [
    { label: 'Completed', value: 'completed' },
    { label: 'Not Started', value: 'not_started' },
    { label: 'In Progress', value: 'in_progress' },
  ],
  difficulty: [
    { label: 'Beginner', value: 'beginner' },
    { label: 'Intermediate', value: 'intermediate' },
    { label: 'Advanced', value: 'advanced' },
  ],
  experience: [
    { label: 'Entry (0–2 years)', value: 'entry' },
    { label: 'Mid (3–5 years)', value: 'mid' },
    { label: 'Senior (6–9 years)', value: 'senior' },
    { label: 'Expert (10+ years)', value: 'expert' },
  ],
  industry: [
    { label: 'Technology', value: 'technology' },
    { label: 'Finance', value: 'finance' },
    { label: 'Healthcare', value: 'healthcare' },
    { label: 'Education', value: 'education' },
    { label: 'Logistics', value: 'logistics' },
    { label: 'Other', value: 'other' },
  ],
  salary: [
    { label: '$0 – $5.000', value: '0-5000' },
    { label: '$5.000 – $10.000', value: '5000-10000' },
    { label: '$10.000 – $15.000', value: '10000-15000' },
    { label: '$15.000 – $20.000', value: '15000-20000' },
  ],
  workType: [
    { label: 'Full-time', value: 'fulltime' },
    { label: 'Part-time', value: 'parttime' },
    { label: 'Contract', value: 'contract' },
    { label: 'Freelance', value: 'freelance' },
    { label: 'Remote only', value: 'remote' },
  ],
};

const renderAccordion = (
  title: string,
  items: { label: string; value: string }[]
) => (
  <Accordion defaultOpen={true}>
    <Accordion.Header>
      {({ open }) => (
        <div className="mb-2 flex w-full cursor-pointer items-center justify-between text-sm">
          <div>{title}</div>
          <UpIcon
            className={cn(
              'h-5 w-5 transform text-gray-500 transition-transform duration-300',
              open && 'rotate-180'
            )}
          />
        </div>
      )}
    </Accordion.Header>
    <Accordion.Body className="pl-6">
      <div className="space-y-2">
        {items.map(({ label, value }) => (
          <label key={value} className="flex items-center space-x-2">
            <Checkbox label={label} variant="flat" size="sm" value={value} />
          </label>
        ))}
      </div>
    </Accordion.Body>
  </Accordion>
);

export default function CandidateFilter() {
  const [skills, setSkills] = useState<{ label: string; value: string }[]>([]);
  const [skillInput, setSkillInput] = useState('');

  const handleSkillKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && skillInput.trim() !== '') {
      e.preventDefault();
      const value = skillInput.trim();
      if (!skills.some((s) => s.value === value)) {
        setSkills([...skills, { label: value, value }]);
      }
      setSkillInput('');
    }
  };

  const removeSkill = (value: string) => {
    setSkills(skills.filter((s) => s.value !== value));
  };

  return (
    <div className="col-span-12 md:col-span-4 lg:col-span-3 space-y-6 rounded-lg border border-[#DEDEDE] bg-white p-4 shadow-lg h-fit">
      {renderAccordion('AI Match Score', filterOptions.matchScore)}
      {renderAccordion('Simulation Performance', filterOptions.simulation)}
      {renderAccordion('Difficulty', filterOptions.difficulty)}
      {renderAccordion('Experience Level', filterOptions.experience)}

      <Accordion defaultOpen={true}>
        <Accordion.Header>
          {({ open }) => (
            <div className="mb-2 flex w-full cursor-pointer items-center justify-between text-sm">
              <div>Skills</div>
              <UpIcon
                className={cn(
                  'h-5 w-5 transform text-gray-500 transition-transform duration-300',
                  open && 'rotate-180'
                )}
              />
            </div>
          )}
        </Accordion.Header>
        <Accordion.Body className="">
          <div className="mt-2 flex flex-wrap gap-2">
            <Input
              className="w-full [&_*]:rounded-md [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#FAFAFA] [&_*]:px-1 [&_*]:!outline-none [&_*]:!ring-0"
              inputClassName="!border-0 !ring-0 focus:!ring-0 p-0 text-sm"
              placeholder="Add Skills"
              value={skillInput}
              onChange={(e) => setSkillInput(e.target.value)}
              onKeyDown={handleSkillKeyDown}
            />
            <div className="mt-2 flex flex-wrap gap-2">
              {skills.map((skill) => (
                <span
                  className="flex items-center gap-2 rounded-full border border-[#E1E1E1] bg-white px-2 py-1 text-[12px]"
                  key={skill.value}
                >
                  <span>{skill.label}</span>
                  <span
                    className="cursor-pointer rounded-full border border-[#E1E1E1] p-0.5 hover:bg-[#E1E1E1]"
                    onClick={() => removeSkill(skill.value)}
                  >
                    <CloseIcon className="h-3 w-3" />
                  </span>
                </span>
              ))}
            </div>
          </div>
        </Accordion.Body>
      </Accordion>

      {renderAccordion('Industry', filterOptions.industry)}
      {renderAccordion('Expected Salary (Monthly USD)', filterOptions.salary)}
      {renderAccordion('Work Type', filterOptions.workType)}
    </div>
  );
}
