'use client';

import { Checkbox } from 'rizzui';
import { useState } from 'react';
import Image from 'next/image';
import Pagination from '../pagination';

const candidates = [
  {
    name: '<PERSON>',
    avatar: '/employer/avatar-albert.jpeg',
    role: 'Frontend Developer – E-Commerce',
    location: 'Singapore',
    experience: '5 years',
    simulation: '92%',
    skills: 'React.js, TailwindCSS, TypeScript',
    lastActive: '3 days ago',
    aiMatch: '91%',
    match: '86',
  },
  {
    name: '<PERSON>',
    avatar: '/employer/avatar-arlene.jpeg',
    role: 'DevOps Engineer – SaaS',
    location: 'Brazil (Remote)',
    experience: '6 years',
    simulation: '80%',
    skills: 'A<PERSON>, Docker, CI/CD, Terraform',
    lastActive: '1 days ago',
    aiMatch: '85%',
    match: '86',
  },
  {
    name: '<PERSON><PERSON>',
    avatar: '/employer/avatar-cody.jpeg',
    role: 'Data Analyst – Fintech',
    location: 'India',
    experience: '3 years',
    simulation: '73%',
    skills: 'SQL, Power BI, Python',
    lastActive: '5 days ago',
    aiMatch: '78%',
    match: '86',
  },
  {
    name: '<PERSON>',
    avatar: '/employer/avatar-albert.jpeg',
    role: 'Backend Developer – Logistics',
    location: 'Japan',
    experience: '7 years',
    simulation: '90%',
    skills: 'NodeJS, PostgreSQL, Kafka',
    lastActive: '12 hours ago',
    aiMatch: '94%',
    match: '86',
  },
  {
    name: 'Sarah Nguyen',
    avatar: '/employer/avatar-cody.jpeg',
    role: 'Frontend Developer – E-Commerce',
    location: 'Australia',
    experience: '5 years',
    simulation: '79%',
    skills: 'TensorFlow, Python, NLP',
    lastActive: '3 days ago',
    aiMatch: '82%',
    match: '86',
  },
];

export default function CandidateList() {
  const [page, setPage] = useState(1);

  return (
    <div className="col-span-12 space-y-4 md:col-span-8 lg:col-span-9">
      <div className="space-y-4">
        {candidates.map((c, i) => (
          <div
            key={i}
            className="flex items-start justify-between rounded-lg border p-4 shadow-lg transition-shadow duration-300 hover:shadow-xl"
          >
            <div className="flex gap-4">
              <div>
                <Checkbox variant="flat" size="sm" />
              </div>
              <div>
                <div className="flex items-center gap-3">
                  <Image
                    src={c.avatar}
                    alt={c.name}
                    width={40}
                    height={40}
                    className="h-10 w-10 rounded-full object-cover"
                    loader={({ src }) => src}
                  />
                  <div>
                    <h4 className="text-md font-bold">{c.name}</h4>
                    <p className="text-[12px]">{c.role}</p>
                  </div>
                </div>
                <div className="mt-3">
                  <div className="grid grid-cols-[140px_1fr] gap-y-1 text-sm">
                    <div className="text-gray-400">Location</div>
                    <div className="text-gray-800">{c.location}</div>

                    <div className="text-gray-400">Experience</div>
                    <div className="text-gray-800">{c.experience}</div>

                    <div className="text-gray-400">Simulation Score</div>
                    <div className="text-gray-800">{c.simulation}</div>

                    <div className="text-gray-400">Skills</div>
                    <div className="text-gray-800">{c.skills}</div>

                    <div className="text-gray-400">Last Active</div>
                    <div className="text-gray-800">{c.lastActive}</div>

                    <div className="text-gray-400">AI Match</div>
                    <div className="text-gray-800">{c.aiMatch}</div>
                  </div>
                  <div className="mt-3 flex gap-4">
                    <button className="rounded border border-[#00B074] px-3 py-1 text-sm text-black hover:bg-primary hover:text-white">
                      Send Job
                    </button>
                    <button className="rounded border border-[#00B074] px-3 py-1 text-sm text-black hover:bg-primary hover:text-white">
                      Message
                    </button>
                    <button className="rounded border border-[#00B074] px-3 py-1 text-sm text-black hover:bg-primary hover:text-white">
                      View CV
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-col items-center rounded-md bg-primary px-2 py-0.5 text-white">
              <div className="-mb-1 font-bold">{c.match}%</div>
              <div className="text-[10px]">Match</div>
            </div>
          </div>
        ))}
      </div>

      <div>
        <Pagination
          total={10}
          current={page}
          pageSize={3}
          defaultCurrent={1}
          showLessItems={true}
          onChange={(page: number) => setPage(page)}
          prevIconClassName="py-0 text-gray-500 !leading-[26px]"
          nextIconClassName="py-0 text-gray-500 !leading-[26px]"
          variant="solid"
          className="justify-center [&>.rc-pagination-item-active>a]:!text-white [&>.rc-pagination-item-active]:!border-primary [&>.rc-pagination-item-active]:!bg-primary [&>.rc-pagination-item-active]:!text-white [&>.rc-pagination-item:not(.rc-pagination-item-active)>a]:text-black [&>.rc-pagination-item:not(.rc-pagination-item-active)]:bg-transparent [&>.rc-pagination-item]:rounded-lg"
          // disabled={isLoading}
        />
      </div>
    </div>
  );
}
